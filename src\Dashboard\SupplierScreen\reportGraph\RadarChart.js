import React, { useEffect, useState, useMemo } from "react";
import {
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LabelList,
} from "recharts";

const SubGraph1Radar = ({ supplyData }) => {
  const [chartData, setChartData] = useState([]);

  // Define max values for each category using useMemo to prevent recreation on each render
  const maxValues = useMemo(() => ({
    "Environmental Framework": 40,
    "Sustainability Ambassadorship Framework": 20,
    "Social Stewardship Framework": 10,
    "Occupational Health & Safety Framework": 20,
    "Legal Compliances": 5,
    "Governance Framework": 5,
  }), []);

  useEffect(() => {
    if (supplyData.length > 0) {
      console.log(supplyData, ' Supply Data');

      // Transform supplyData with percentage calculations
      const transformedData = supplyData.map(item => {
        const achievedValue = parseFloat(item.sectionTotalScore);
        const maxValue = maxValues[item.title] || 1; // Default to 1 if maxValue is not defined

        // Calculate percentage, handle division by zero
        let percentageValue = 0;
        if (maxValue > 0) {
          percentageValue = parseFloat(((achievedValue / maxValue) * 100).toFixed(1));
        }

        // Ensure percentage doesn't exceed 100%
        percentageValue = Math.min(percentageValue, 100);

        return {
          category: item.title,
          Achieved: achievedValue,
          Percentage: percentageValue,
          MaxValue: maxValue
        };
      });

      console.log(transformedData, 'Transformed Data');
      setChartData(transformedData);
    }
  }, [supplyData, maxValues]);

  return (
    <div className="container mt-4" style={{ background: "#DAF3EF" }}>
      <h5 className="mb-3 text-center text-dark">
        ESG Score of Calibrated Suppliers (Percentage View)
      </h5>

      <ResponsiveContainer width="100%" height={400}>
        <RadarChart data={chartData} margin={{ top: 20, right: 80, bottom: 20, left: 80 }}>
          <PolarGrid />
          <PolarAngleAxis 
            dataKey="category"
            tick={({ x, y, payload }) => {
              const words = payload.value.split(' ');
              const lineHeight = 15;
              return (
                <g transform={`translate(${x},${y})`}>
                  {words.map((word, index) => (
                    <text
                      key={index}
                      x={0}
                      y={index * lineHeight}
                      dy={0}
                      textAnchor="middle"
                      fill="#666"
                      fontSize="11px"
                    >
                      {word}
                    </text>
                  ))}
                </g>
              );
            }}
          />
          <PolarRadiusAxis
            domain={[0, 100]}
            tickFormatter={(value) => `${value}%`}
          />
          <Radar
            name="Percentage Score"
            dataKey="Percentage"
            stroke="#82ca9d"
            fill="#82ca9d"
            fillOpacity={0.6}
          >
            <LabelList
              formatter={(value) => `${value}%`}
              dataKey="Percentage"
              position="outside"
              offset={15}
              style={{ fill: '#444', fontSize: '12px', fontWeight: 'bold' }}
            />
          </Radar>
          <Tooltip
            formatter={(value, name, props) => {
              if (!props || !props.payload) {
                return [value, name];
              }

              if (name === "Percentage Score" &&
                props.payload.Achieved !== undefined &&
                props.payload.MaxValue !== undefined) {
                return [`${value}% (${props.payload.Achieved} / ${props.payload.MaxValue})`, name];
              }

              return [value, name];
            }}
          />
          <Legend />
        </RadarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default SubGraph1Radar;

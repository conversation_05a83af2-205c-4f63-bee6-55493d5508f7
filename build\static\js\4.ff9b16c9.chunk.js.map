{"version": 3, "sources": ["../../src/utils.js", "../../src/tags.js", "../../src/attrs.js", "../../src/regexp.js", "../../src/purify.js"], "names": ["hasOwnProperty", "Object", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "freeze", "seal", "create", "_ref", "Reflect", "apply", "construct", "fun", "thisValue", "args", "x", "Func", "_construct", "_toConsumableArray", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayPop", "pop", "arrayPush", "push", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "func", "thisArg", "_len", "arguments", "length", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "_transformCaseFunc", "l", "element", "lcElement", "clone", "object", "property", "newObject", "lookupGetter", "prop", "desc", "get", "value", "fallback<PERSON><PERSON><PERSON>", "console", "warn", "html$1", "svg$1", "svgFilters", "svgDisallowed", "mathMl$1", "mathMlDisallowed", "text", "html", "svg", "mathMl", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "document", "_typeof", "createPolicy", "suffix", "ATTR_NAME", "currentScript", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "createDOMPurify", "undefined", "DOMPurify", "root", "version", "removed", "nodeType", "isSupported", "originalDocument", "DocumentFragment", "HTMLTemplateElement", "Node", "Element", "Node<PERSON><PERSON><PERSON>", "_window$NamedNodeMap", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "_document", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "documentMode", "hooks", "createHTMLDocument", "PARSER_MEDIA_TYPE", "MUSTACHE_EXPR$1", "ERB_EXPR$1", "TMPLIT_EXPR$1", "DATA_ATTR$1", "ARIA_ATTR$1", "IS_SCRIPT_OR_DATA$1", "ATTR_WHITESPACE$1", "CUSTOM_ELEMENT$1", "IS_ALLOWED_URI$1", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "concat", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "outerHTML", "remove", "_removeAttribute", "name", "attribute", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "elm", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_basicCustomElementTest", "i", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "attr", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "_attr", "forceKeepAttr", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "oldNode", "returnNode", "toStaticHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmod", "serializedHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";knDAAA,IACEA,EAKEC,OALFD,eACAE,EAIED,OAJFC,eACAC,EAGEF,OAHFE,SACAC,EAEEH,OAFFG,eACAC,EACEJ,OADFI,yBAGIC,EAAyBL,OAAzBK,OAAQC,EAAiBN,OAAjBM,KAAMC,EAAWP,OAAXO,OACpBC,EAA8C,qBAAZC,SAA2BA,QAAvDC,EAANF,EAAME,MAAOC,EAAbH,EAAaG,UAERD,IACHA,EAAQ,SAAUE,EAAKC,EAAWC,GAChC,OAAOF,EAAIF,MAAMG,EAAWC,EAC7B,GAGET,IACHA,EAAS,SAAUU,GACjB,OAAOA,CACR,GAGET,IACHA,EAAO,SAAUS,GACf,OAAOA,CACR,GAGEJ,IACHA,EAAY,SAAUK,EAAMF,GACf,OAAAG,EAAAD,EAAXE,EAAmBJ,GACpB,GAGH,IAAMK,EAAeC,EAAQC,MAAMC,UAAUC,SAEvCC,EAAWJ,EAAQC,MAAMC,UAAUG,KACnCC,EAAYN,EAAQC,MAAMC,UAAUK,MAGpCC,EAAoBR,EAAQS,OAAOP,UAAUQ,aAC7CC,EAAiBX,EAAQS,OAAOP,UAAUU,UAC1CC,EAAcb,EAAQS,OAAOP,UAAUY,OACvCC,EAAgBf,EAAQS,OAAOP,UAAUc,SACzCC,EAAgBjB,EAAQS,OAAOP,UAAUgB,SACzCC,EAAanB,EAAQS,OAAOP,UAAUkB,MAEtCC,EAAarB,EAAQsB,OAAOpB,UAAUqB,MAEtCC,EAAkBC,EAAYC,WAE7B,SAAS1B,EAAQ2B,GACtB,OAAO,SAACC,GAAD,QAAAC,EAAAC,UAAAC,OAAarC,EAAb,IAAAO,MAAA4B,EAAA,EAAAA,EAAA,KAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAatC,EAAbsC,EAAA,GAAAF,UAAAE,GAAA,OAAsB1C,EAAMqC,EAAMC,EAASlC,EAA3C,CACR,CAEM,SAAS+B,EAAYE,GACnB,0BAAAM,EAAAH,UAAAC,OAAIrC,EAAJ,IAAAO,MAAAgC,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAIxC,EAAJwC,GAAAJ,UAAAI,GAAA,OAAa3C,EAAUoC,EAAMjC,EAA7B,CACR,CAGM,SAASyC,EAASC,EAAKC,EAAOC,GAAmB,IAAAC,EACtDD,EAAiB,QAAAC,EAAGD,SAAH,IAAAC,IAAwB/B,EACrC3B,GAIFA,EAAeuD,EAAK,MAIf,IADP,IAAII,EAAIH,EAAMN,OACPS,KAAK,CACV,IAAIC,EAAUJ,EAAMG,GACpB,GAAuB,kBAAZC,EAAsB,CAC/B,IAAMC,EAAYJ,EAAkBG,GAChCC,IAAcD,IAEX3D,EAASuD,KACZA,EAAMG,GAAKE,GAGbD,EAAUC,EAEb,CAEDN,EAAIK,IAAW,CAChB,CAED,OAAOL,CACR,CAGM,SAASO,EAAMC,GACpB,IAEIC,EAFEC,EAAY3D,EAAO,MAGpB,IAAA0D,KAAYD,GACmC,IAA9CtD,EAAMX,EAAgBiE,EAAQ,CAACC,MACjCC,EAAUD,GAAYD,EAAOC,IAIjC,OAAOC,CACR,CAMD,SAASC,EAAaH,EAAQI,GACrB,KAAW,OAAXJ,GAAiB,CACtB,IAAMK,EAAOjE,EAAyB4D,EAAQI,GAC9C,GAAIC,EAAM,CACJ,GAAAA,EAAKC,IACP,OAAOlD,EAAQiD,EAAKC,KAGtB,GAA0B,oBAAfD,EAAKE,MACd,OAAOnD,EAAQiD,EAAKE,MAEvB,CAEDP,EAAS7D,EAAe6D,EACzB,CAEQ,SAAAQ,EAAcX,GAErB,OADAY,QAAQC,KAAK,qBAAsBb,GAC5B,IACR,CAED,OAAOW,CACR,CCjIM,IAAMG,EAAOtE,EAAO,CACzB,IACA,OACA,UACA,UACA,OACA,UACA,QACA,QACA,IACA,MACA,MACA,MACA,QACA,aACA,OACA,KACA,SACA,SACA,UACA,SACA,OACA,OACA,MACA,WACA,UACA,OACA,WACA,KACA,YACA,MACA,UACA,MACA,SACA,MACA,MACA,KACA,KACA,UACA,KACA,WACA,aACA,SACA,OACA,SACA,OACA,KACA,KACA,KACA,KACA,KACA,KACA,OACA,SACA,SACA,KACA,OACA,IACA,MACA,QACA,MACA,MACA,QACA,SACA,KACA,OACA,MACA,OACA,UACA,OACA,WACA,QACA,MACA,OACA,KACA,WACA,SACA,SACA,IACA,UACA,MACA,WACA,IACA,KACA,KACA,OACA,IACA,OACA,UACA,SACA,SACA,QACA,SACA,SACA,OACA,SACA,SACA,QACA,MACA,UACA,MACA,QACA,QACA,KACA,WACA,WACA,QACA,KACA,QACA,OACA,KACA,QACA,KACA,IACA,KACA,MACA,QACA,QAIWuE,EAAMvE,EAAO,CACxB,MACA,IACA,WACA,cACA,eACA,eACA,gBACA,mBACA,SACA,WACA,OACA,OACA,UACA,SACA,OACA,IACA,QACA,WACA,QACA,QACA,OACA,iBACA,SACA,OACA,WACA,QACA,OACA,UACA,UACA,WACA,iBACA,OACA,OACA,QACA,SACA,SACA,OACA,WACA,QACA,OACA,QACA,OACA,UAGWwE,EAAaxE,EAAO,CAC/B,UACA,gBACA,sBACA,cACA,mBACA,oBACA,oBACA,iBACA,UACA,UACA,UACA,UACA,UACA,iBACA,UACA,UACA,cACA,eACA,WACA,eACA,qBACA,cACA,SACA,iBAOWyE,EAAgBzE,EAAO,CAClC,UACA,gBACA,SACA,UACA,eACA,YACA,mBACA,iBACA,gBACA,gBACA,gBACA,QACA,YACA,OACA,eACA,YACA,UACA,gBACA,SACA,MACA,aACA,UACA,QAGW0E,EAAS1E,EAAO,CAC3B,OACA,WACA,SACA,UACA,QACA,SACA,KACA,aACA,gBACA,KACA,KACA,QACA,UACA,WACA,QACA,OACA,KACA,SACA,QACA,SACA,OACA,OACA,UACA,SACA,MACA,QACA,MACA,SACA,eAKW2E,EAAmB3E,EAAO,CACrC,UACA,cACA,aACA,WACA,YACA,UACA,UACA,SACA,SACA,QACA,YACA,aACA,iBACA,cACA,SAGW4E,EAAO5E,EAAO,CAAC,UCpRf6E,EAAO7E,EAAO,CACzB,SACA,SACA,QACA,MACA,iBACA,eACA,uBACA,WACA,aACA,UACA,SACA,UACA,cACA,cACA,UACA,OACA,QACA,QACA,QACA,OACA,UACA,WACA,eACA,SACA,cACA,WACA,WACA,UACA,MACA,WACA,0BACA,wBACA,WACA,YACA,UACA,eACA,OACA,MACA,UACA,SACA,SACA,OACA,OACA,WACA,KACA,YACA,YACA,QACA,OACA,QACA,OACA,OACA,UACA,OACA,MACA,MACA,YACA,QACA,SACA,MACA,YACA,WACA,QACA,OACA,QACA,UACA,aACA,SACA,OACA,UACA,UACA,cACA,cACA,SACA,UACA,UACA,aACA,WACA,MACA,WACA,MACA,WACA,OACA,OACA,UACA,aACA,QACA,WACA,QACA,OACA,QACA,OACA,UACA,QACA,MACA,SACA,OACA,QACA,UACA,WACA,QACA,YACA,OACA,SACA,SACA,QACA,QACA,QACA,SAGW8E,EAAM9E,EAAO,CACxB,gBACA,aACA,WACA,qBACA,SACA,gBACA,gBACA,UACA,gBACA,iBACA,QACA,OACA,KACA,QACA,OACA,gBACA,YACA,YACA,QACA,sBACA,8BACA,gBACA,kBACA,KACA,KACA,IACA,KACA,KACA,kBACA,YACA,UACA,UACA,MACA,WACA,YACA,MACA,OACA,eACA,YACA,SACA,cACA,cACA,gBACA,cACA,YACA,mBACA,eACA,aACA,eACA,cACA,KACA,KACA,KACA,KACA,aACA,WACA,gBACA,oBACA,SACA,OACA,KACA,kBACA,KACA,MACA,IACA,KACA,KACA,KACA,KACA,UACA,YACA,aACA,WACA,OACA,eACA,iBACA,eACA,mBACA,iBACA,QACA,aACA,aACA,eACA,eACA,cACA,cACA,mBACA,YACA,MACA,OACA,QACA,SACA,OACA,MACA,OACA,aACA,SACA,WACA,UACA,QACA,SACA,cACA,SACA,WACA,cACA,OACA,aACA,sBACA,mBACA,eACA,SACA,gBACA,sBACA,iBACA,IACA,KACA,KACA,SACA,OACA,OACA,cACA,YACA,UACA,SACA,SACA,QACA,OACA,kBACA,mBACA,mBACA,eACA,cACA,eACA,cACA,aACA,eACA,mBACA,oBACA,iBACA,kBACA,oBACA,iBACA,SACA,eACA,QACA,eACA,iBACA,WACA,UACA,UACA,YACA,mBACA,cACA,kBACA,iBACA,aACA,OACA,KACA,KACA,UACA,SACA,UACA,aACA,UACA,aACA,gBACA,gBACA,QACA,eACA,OACA,eACA,mBACA,mBACA,IACA,KACA,KACA,QACA,IACA,KACA,KACA,IACA,eAGW+E,EAAS/E,EAAO,CAC3B,SACA,cACA,QACA,WACA,QACA,eACA,cACA,aACA,aACA,QACA,MACA,UACA,eACA,WACA,QACA,QACA,SACA,OACA,KACA,UACA,SACA,gBACA,SACA,SACA,iBACA,YACA,WACA,cACA,UACA,UACA,gBACA,WACA,WACA,OACA,WACA,WACA,aACA,UACA,SACA,SACA,cACA,gBACA,uBACA,YACA,YACA,aACA,WACA,iBACA,iBACA,YACA,UACA,QACA,UAGWgF,EAAMhF,EAAO,CACxB,aACA,SACA,cACA,YACA,gBCrWWiF,EAAgBhF,EAAK,6BACrBiF,EAAWjF,EAAK,yBAChBkF,EAAclF,EAAK,iBACnBmF,EAAYnF,EAAK,8BACjBoF,EAAYpF,EAAK,kBACjBqF,EAAiBrF,EAC5B,yFAEWsF,GAAoBtF,EAAK,yBACzBuF,GAAkBvF,EAC7B,+DAEWwF,GAAexF,EAAK,WACpByF,GAAiBzF,EAAK,4BCK7B0F,GAAY,iBAAyB,qBAAXC,OAAyB,KAAOA,MAA9C,EAUZC,GAA4B,SAAUC,EAAcC,GAEtD,GAAwB,WAAxBC,EAAOF,IAC8B,oBAA9BA,EAAaG,aAEpB,OAAO,KAML,IAAAC,EAAS,KACPC,EAAY,wBAEhBJ,EAASK,eACTL,EAASK,cAAcC,aAAaF,KAEpCD,EAASH,EAASK,cAAcE,aAAaH,IAGzC,IAAAI,EAAa,aAAeL,EAAS,IAAMA,EAAS,IAEtD,IACF,OAAOJ,EAAaG,aAAaM,EAAY,CAC3CC,WAAW,SAAA3B,GACT,OAAOA,CACR,EACD4B,gBAAgB,SAAAC,GACd,OAAOA,CACR,GAEJ,CAAC,MAAOC,GAOP,OAHAvC,QAAQC,KACN,uBAAyBkC,EAAa,0BAEjC,IACR,CACF,EAED,SAASK,KAAgB,IAAAhB,EAAS/C,UAAAC,OAAA,QAAA+D,IAAAhE,UAAA,GAAAA,UAAA,GAAA8C,KAC1BmB,EAAY,SAACC,GAAS,OAAAH,GAAgBG,EAA1B,EAclB,GARAD,EAAUE,QAAU,QAMpBF,EAAUG,QAAU,IAEfrB,IAAWA,EAAOG,UAAyC,IAA7BH,EAAOG,SAASmB,SAKjD,OAFAJ,EAAUK,aAAc,EAEjBL,EAGT,IAAMM,EAAmBxB,EAAOG,SAE1BA,EAAaH,EAAbG,SAEJsB,EASEzB,EATFyB,iBACAC,EAQE1B,EARF0B,oBACAC,EAOE3B,EAPF2B,KACAC,EAME5B,EANF4B,QACAC,EAKE7B,EALF6B,WAKEC,EAAA9B,EAJF+B,oBANF,IAAAD,EAMiB9B,EAAO+B,cAAgB/B,EAAOgC,gBAN/CF,EAOEG,EAGEjC,EAHFiC,gBACAC,EAEElC,EAFFkC,UACAhC,EACEF,EADFE,aAGIiC,EAAmBP,EAAQvG,UAE3B+G,EAAYlE,EAAaiE,EAAkB,aAC3CE,EAAiBnE,EAAaiE,EAAkB,eAChDG,EAAgBpE,EAAaiE,EAAkB,cAC/CI,EAAgBrE,EAAaiE,EAAkB,cAQrD,GAAmC,oBAAxBT,EAAoC,CAC7C,IAAMc,EAAWrC,EAASsC,cAAc,YACpCD,EAASE,SAAWF,EAASE,QAAQC,gBACvCxC,EAAWqC,EAASE,QAAQC,cAE/B,CAED,IAAMC,GAAqB3C,GACzBC,EACAsB,GAEIqB,GAAYD,GAAqBA,GAAmBhC,WAAW,IAAM,GAE3EkC,GAKI3C,EAJF4C,GADFD,GACEC,eACAC,GAFFF,GAEEE,mBACAC,GAHFH,GAGEG,uBACAC,GAJFJ,GAIEI,qBAEMC,GAAe3B,EAAf2B,WAEJC,GAAe,CAAC,EAChB,IACFA,GAAetF,EAAMqC,GAAUiD,aAAejD,EAASiD,aAAe,CAAC,CACxE,CAAC,MAAOrC,IAAI,CAET,IAAAsC,GAAQ,CAAC,EAKbnC,EAAUK,YACiB,oBAAlBgB,GACPQ,SACsC9B,IAAtC8B,GAAeO,oBACE,IAAjBF,GAEF,IA6NIG,GAGA9F,GA/NF+F,GAQEnE,EAPFoE,GAOEnE,EANFoE,GAMEnE,EALFoE,GAKEnE,EAJFoE,GAIEnE,EAHFoE,GAGElE,GAFFmE,GAEElE,GADFmE,GACEjE,GAEEkE,GAAmBtE,EAQrBuE,GAAe,KACbC,GAAuB5G,EAAS,CAAC,EAAF,GAAA6G,OAAAlJ,EAChCyD,GADgCzD,EAEhC0D,GACA1D,EAAA2D,GACA3D,EAAA6D,GACA7D,EAAA+D,KAIDoF,GAAe,KACbC,GAAuB/G,EAAS,CAAC,EAClC,GAAA6G,OAAAlJ,EAAAgE,GADgChE,EAEhCiE,GAFgCjE,EAGhCkE,GACAlE,EAAAmE,KASDkF,GAA0BvK,OAAOM,KACnCN,OAAOO,OAAO,KAAM,CAClBiK,aAAc,CACZC,UAAU,EACVC,cAAc,EACdC,YAAY,EACZpG,MAAO,MAETqG,mBAAoB,CAClBH,UAAU,EACVC,cAAc,EACdC,YAAY,EACZpG,MAAO,MAETsG,+BAAgC,CAC9BJ,UAAU,EACVC,cAAc,EACdC,YAAY,EACZpG,OAAO,MAMTuG,GAAc,KAGdC,GAAc,KAGdC,IAAkB,EAGlBC,IAAkB,EAGlBC,IAA0B,EAI1BC,IAA2B,EAK3BC,IAAqB,EAGrBC,IAAiB,EAGjBC,IAAa,EAIbC,IAAa,EAMbC,IAAa,EAIbC,IAAsB,EAItBC,IAAsB,EAKtBC,IAAe,EAefC,IAAuB,EACrBC,GAA8B,gBAGhCC,IAAe,EAIfC,IAAW,EAGXC,GAAe,CAAC,EAGhBC,GAAkB,KAChBC,GAA0B3I,EAAS,CAAC,EAAG,CAC3C,iBACA,QACA,WACA,OACA,gBACA,OACA,SACA,OACA,KACA,KACA,KACA,KACA,QACA,UACA,WACA,WACA,YACA,SACA,QACA,MACA,WACA,QACA,QACA,QACA,QAIE4I,GAAgB,KACdC,GAAwB7I,EAAS,CAAC,EAAG,CACzC,QACA,QACA,MACA,SACA,QACA,UAIE8I,GAAsB,KACpBC,GAA8B/I,EAAS,CAAC,EAAG,CAC/C,MACA,QACA,MACA,KACA,QACA,OACA,UACA,cACA,OACA,UACA,QACA,QACA,QACA,UAGIgJ,GAAmB,qCACnBC,GAAgB,6BAChBC,GAAiB,+BAEnBC,GAAYD,GACZE,IAAiB,EAGjBC,GAAqB,KACnBC,GAA6BtJ,EACjC,CAAC,EACD,CAACgJ,GAAkBC,GAAeC,IAClC1K,GAKI+K,GAA+B,CAAC,wBAAyB,aACzDC,GAA4B,YAI9BC,GAAS,KAKPC,GAAc7G,EAASsC,cAAc,QAErCwE,GAAoB,SAAUC,GAClC,OAAOA,aAAqBzK,QAAUyK,aAAqBC,QAC5D,EAQKC,GAAe,SAAUC,GACzBN,IAAUA,KAAWM,IAKpBA,GAAsB,WAAfjH,EAAOiH,KACjBA,EAAM,CAAC,GAITA,EAAMvJ,EAAMuJ,GAEZ9D,GAGOA,IAD4D,IAAjEsD,GAA6BxK,QAAQgL,EAAI9D,mBAChBuD,GACAO,EAAI9D,kBAG/B9F,GACwB,0BAAtB8F,GACIzH,EACAH,EAGNsI,GACE,iBAAkBoD,EACd/J,EAAS,CAAC,EAAG+J,EAAIpD,aAAcxG,IAC/ByG,GACNE,GACE,iBAAkBiD,EACd/J,EAAS,CAAC,EAAG+J,EAAIjD,aAAc3G,IAC/B4G,GACNsC,GACE,uBAAwBU,EACpB/J,EAAS,CAAC,EAAG+J,EAAIV,mBAAoB7K,GACrC8K,GACNR,GACE,sBAAuBiB,EACnB/J,EACEQ,EAAMuI,IACNgB,EAAIC,kBACJ7J,IAEF4I,GACNH,GACE,sBAAuBmB,EACnB/J,EACEQ,EAAMqI,IACNkB,EAAIE,kBACJ9J,IAEF0I,GACNH,GACE,oBAAqBqB,EACjB/J,EAAS,CAAC,EAAG+J,EAAIrB,gBAAiBvI,IAClCwI,GACNpB,GACE,gBAAiBwC,EACb/J,EAAS,CAAC,EAAG+J,EAAIxC,YAAapH,IAC9B,CAAC,EACPqH,GACE,gBAAiBuC,EACb/J,EAAS,CAAC,EAAG+J,EAAIvC,YAAarH,IAC9B,CAAC,EACPsI,GAAe,iBAAkBsB,GAAMA,EAAItB,aAC3ChB,IAA0C,IAAxBsC,EAAItC,gBACtBC,IAA0C,IAAxBqC,EAAIrC,gBACtBC,GAA0BoC,EAAIpC,0BAA2B,EACzDC,IAA4D,IAAjCmC,EAAInC,yBAC/BC,GAAqBkC,EAAIlC,qBAAsB,EAC/CC,GAAiBiC,EAAIjC,iBAAkB,EACvCG,GAAa8B,EAAI9B,aAAc,EAC/BC,GAAsB6B,EAAI7B,sBAAuB,EACjDC,GAAsB4B,EAAI5B,sBAAuB,EACjDH,GAAa+B,EAAI/B,aAAc,EAC/BI,IAAoC,IAArB2B,EAAI3B,aACnBC,GAAuB0B,EAAI1B,uBAAwB,EACnDE,IAAoC,IAArBwB,EAAIxB,aACnBC,GAAWuB,EAAIvB,WAAY,EAC3B9B,GAAiBqD,EAAIG,oBAAsBxD,GAC3CyC,GAAYY,EAAIZ,WAAaD,GAC7BlC,GAA0B+C,EAAI/C,yBAA2B,CAAC,EAExD+C,EAAI/C,yBACJ2C,GAAkBI,EAAI/C,wBAAwBC,gBAE9CD,GAAwBC,aACtB8C,EAAI/C,wBAAwBC,cAI9B8C,EAAI/C,yBACJ2C,GAAkBI,EAAI/C,wBAAwBK,sBAE9CL,GAAwBK,mBACtB0C,EAAI/C,wBAAwBK,oBAI9B0C,EAAI/C,yBAEF,mBADK+C,EAAI/C,wBAAwBM,iCAGnCN,GAAwBM,+BACtByC,EAAI/C,wBAAwBM,gCAG5BO,KACFH,IAAkB,GAGhBQ,KACFD,IAAa,GAIXQ,KACF9B,GAAe3G,EAAS,CAAC,EAAOrC,EAAA+D,IAChCoF,GAAe,IACW,IAAtB2B,GAAa9G,OACf3B,EAAS2G,GAAcvF,GACvBpB,EAAS8G,GAAcnF,KAGA,IAArB8G,GAAa7G,MACf5B,EAAS2G,GAActF,GACvBrB,EAAS8G,GAAclF,GACvB5B,EAAS8G,GAAchF,KAGO,IAA5B2G,GAAanH,aACftB,EAAS2G,GAAcrF,GACvBtB,EAAS8G,GAAclF,GACvB5B,EAAS8G,GAAchF,KAGG,IAAxB2G,GAAa5G,SACf7B,EAAS2G,GAAcnF,GACvBxB,EAAS8G,GAAcjF,GACvB7B,EAAS8G,GAAchF,KAKvBiI,EAAII,WACFxD,KAAiBC,KACnBD,GAAenG,EAAMmG,KAGvB3G,EAAS2G,GAAcoD,EAAII,SAAUhK,KAGnC4J,EAAIK,WACFtD,KAAiBC,KACnBD,GAAetG,EAAMsG,KAGvB9G,EAAS8G,GAAciD,EAAIK,SAAUjK,KAGnC4J,EAAIC,mBACNhK,EAAS8I,GAAqBiB,EAAIC,kBAAmB7J,IAGnD4J,EAAIrB,kBACFA,KAAoBC,KACtBD,GAAkBlI,EAAMkI,KAG1B1I,EAAS0I,GAAiBqB,EAAIrB,gBAAiBvI,KAI7CoI,KACF5B,GAAa,UAAW,GAItBmB,IACF9H,EAAS2G,GAAc,CAAC,OAAQ,OAAQ,SAItCA,GAAa0D,QACfrK,EAAS2G,GAAc,CAAC,iBACjBY,GAAY+C,OAKjBxN,GACFA,EAAOiN,GAGTN,GAASM,EACV,EAEKQ,GAAiCvK,EAAS,CAAC,EAAG,CAClD,KACA,KACA,KACA,KACA,UAGIwK,GAA0BxK,EAAS,CAAC,EAAG,CAC3C,gBACA,OACA,QACA,mBAOIyK,GAA+BzK,EAAS,CAAC,EAAG,CAChD,QACA,QACA,OACA,IACA,WAMI0K,GAAe1K,EAAS,CAAC,EAAGqB,GAClCrB,EAAS0K,GAAcpJ,GACvBtB,EAAS0K,GAAcnJ,GAEjB,IAAAoJ,GAAkB3K,EAAS,CAAC,EAAGwB,GACrCxB,EAAS2K,GAAiBlJ,GAU1B,IAAMmJ,GAAuB,SAAUtK,GACrC,IAAIuK,EAAS5F,EAAc3E,GAItBuK,GAAWA,EAAOC,UACrBD,EAAS,CACPE,aAAc5B,GACd2B,QAAS,aAIb,IAAMA,EAAUzM,EAAkBiC,EAAQwK,SACpCE,EAAgB3M,EAAkBwM,EAAOC,SAE/C,QAAKzB,GAAmB/I,EAAQyK,gBAI5BzK,EAAQyK,eAAiB9B,GAIvB4B,EAAOE,eAAiB7B,GACP,QAAZ4B,EAMLD,EAAOE,eAAiB/B,GAEZ,QAAZ8B,IACmB,mBAAlBE,GACCT,GAA+BS,IAM9BC,QAAQP,GAAaI,IAG1BxK,EAAQyK,eAAiB/B,GAIvB6B,EAAOE,eAAiB7B,GACP,SAAZ4B,EAKLD,EAAOE,eAAiB9B,GACP,SAAZ6B,GAAsBN,GAAwBQ,GAKhDC,QAAQN,GAAgBG,IAG7BxK,EAAQyK,eAAiB7B,KAKzB2B,EAAOE,eAAiB9B,KACvBuB,GAAwBQ,OAMzBH,EAAOE,eAAiB/B,KACvBuB,GAA+BS,MAQ/BL,GAAgBG,KAChBL,GAA6BK,KAAaJ,GAAaI,MAMpC,0BAAtB7E,KACAoD,GAAmB/I,EAAQyK,eAU9B,EAOKG,GAAe,SAAUC,GAC7BhN,EAAUyF,EAAUG,QAAS,CAAEzD,QAAS6K,IACpC,IAEFA,EAAKC,WAAWC,YAAYF,EAC7B,CAAC,MAAO1H,IACH,IACF0H,EAAKG,UAAY/F,EAClB,CAAC,MAAO9B,IACP0H,EAAKI,QACN,CACF,CACF,EAQKC,GAAmB,SAAUC,EAAMN,GACnC,IACFhN,EAAUyF,EAAUG,QAAS,CAC3B2H,UAAWP,EAAKQ,iBAAiBF,GACjCG,KAAMT,GAET,CAAC,MAAO1H,IACPtF,EAAUyF,EAAUG,QAAS,CAC3B2H,UAAW,KACXE,KAAMT,GAET,CAKG,GAHJA,EAAKU,gBAAgBJ,GAGR,OAATA,IAAkB3E,GAAa2E,GAC7B,GAAAxD,IAAcC,GACZ,IACFgD,GAAaC,EACd,CAAC,MAAO1H,IAAI,MAET,IACF0H,EAAKW,aAAaL,EAAM,GACzB,CAAC,MAAOhI,IAAI,CAGlB,EAQKsI,GAAgB,SAAUC,GAE9B,IAAIC,EACAC,EAEJ,GAAIlE,GACFgE,EAAQ,oBAAsBA,MACzB,CAEL,IAAMG,EAAUzN,EAAYsN,EAAO,eACnCE,EAAoBC,GAAWA,EAAQ,EACxC,CAGuB,0BAAtBlG,IACAkD,KAAcD,KAGd8C,EACE,iEACAA,EACA,kBAGE,IAAAI,EAAe9G,GACjBA,GAAmBhC,WAAW0I,GAC9BA,EAKA,GAAA7C,KAAcD,GACZ,IACF+C,GAAM,IAAIrH,GAAYyH,gBAAgBD,EAAcnG,GACrD,CAAC,MAAOxC,IAAI,CAIf,IAAKwI,IAAQA,EAAIK,gBAAiB,CAChCL,EAAMxG,GAAe8G,eAAepD,GAAW,WAAY,MACvD,IACF8C,EAAIK,gBAAgBE,UAAYpD,GAC5B7D,GACA6G,CACL,CAAC,MAAO3I,IAAG,CAGb,CAEK,IAAAgJ,EAAOR,EAAIQ,MAAQR,EAAIK,gBAUzB,OARAN,GAASE,GACXO,EAAKC,aACH7J,EAAS8J,eAAeT,GACxBO,EAAKG,WAAW,IAAM,MAKtBzD,KAAcD,GACTtD,GAAqBiH,KAC1BZ,EACAnE,GAAiB,OAAS,QAC1B,GAGGA,GAAiBmE,EAAIK,gBAAkBG,CAC/C,EAQKK,GAAkB,SAAUjJ,GACzB,OAAA6B,GAAmBmH,KACxBhJ,EAAKwB,eAAiBxB,EACtBA,EAEAU,EAAWwI,aACTxI,EAAWyI,aACXzI,EAAW0I,UACX1I,EAAW2I,4BACX3I,EAAW4I,mBACb,MACA,EAEH,EAQKC,GAAe,SAAUC,GAC7B,OACEA,aAAe1I,IACU,kBAAjB0I,EAAIC,UACiB,kBAApBD,EAAIE,aACgB,oBAApBF,EAAIhC,eACTgC,EAAIG,sBAAsB/I,IACG,oBAAxB4I,EAAIxB,iBACiB,oBAArBwB,EAAIvB,cACiB,kBAArBuB,EAAItC,cACiB,oBAArBsC,EAAIX,cACkB,oBAAtBW,EAAII,cAEhB,EAQKC,GAAU,SAAUjN,GACxB,MAAuB,WAAhBqC,EAAOuB,GACV5D,aAAkB4D,EAClB5D,GACoB,WAAlBqC,EAAOrC,IACoB,kBAApBA,EAAOuD,UACa,kBAApBvD,EAAO6M,QACrB,EAUKK,GAAe,SAAUC,EAAYC,EAAaC,GACjD/H,GAAM6H,IAIXhQ,EAAamI,GAAM6H,IAAa,SAACG,GAC/BA,EAAKlB,KAAKjJ,EAAWiK,EAAaC,EAAMrE,GACzC,GACF,EAYKuE,GAAoB,SAAUH,GAClC,IAAIzI,EAMJ,GAHAuI,GAAa,yBAA0BE,EAAa,MAGhDT,GAAaS,GAEf,OADA3C,GAAa2C,IACN,EAIL,GAAA3O,EAAW,kBAAmB2O,EAAYP,UAE5C,OADApC,GAAa2C,IACN,EAIT,IAAM/C,EAAU3K,GAAkB0N,EAAYP,UAU5C,GAPFK,GAAa,sBAAuBE,EAAa,CAC/C/C,UACAmD,YAAatH,KAKbkH,EAAYJ,kBACXC,GAAQG,EAAYK,sBACnBR,GAAQG,EAAYzI,WACnBsI,GAAQG,EAAYzI,QAAQ8I,qBAC/BhP,EAAW,UAAW2O,EAAYrB,YAClCtN,EAAW,UAAW2O,EAAYN,aAGlC,OADArC,GAAa2C,IACN,EAIT,GACc,WAAZ/C,GACA5L,EAAW,aAAc2O,EAAYrB,WAGrC,OADAtB,GAAa2C,IACN,EAIT,GAA6B,IAAzBA,EAAY7J,SAEd,OADAkH,GAAa2C,IACN,EAIL,IAAClH,GAAamE,IAAYvD,GAAYuD,GAAU,CAE9C,IAACvD,GAAYuD,IAAYqD,GAAwBrD,GAAU,CAC7D,GACE9D,GAAwBC,wBAAwB9H,QAChDD,EAAW8H,GAAwBC,aAAc6D,GAEjD,OAAO,EACT,GACE9D,GAAwBC,wBAAwB4C,UAChD7C,GAAwBC,aAAa6D,GAErC,OAAO,CACV,CAGD,GAAIvC,KAAiBG,GAAgBoC,GAAU,CACvC,IAAAM,EAAanG,EAAc4I,IAAgBA,EAAYzC,WACvDwB,EAAa5H,EAAc6I,IAAgBA,EAAYjB,WAEzD,GAAAA,GAAcxB,EAGhB,IAFA,IAESgD,EAFUxB,EAAWhN,OAEJ,EAAGwO,GAAK,IAAKA,EACrChD,EAAWsB,aACT5H,EAAU8H,EAAWwB,IAAI,GACzBrJ,EAAe8I,GAItB,CAGD,OADA3C,GAAa2C,IACN,CACR,CAGG,OAAAA,aAAuBvJ,IAAYsG,GAAqBiD,IAC1D3C,GAAa2C,IACN,GAKM,aAAZ/C,GACa,YAAZA,GACY,aAAZA,IACF5L,EAAW,8BAA+B2O,EAAYrB,YAOpD3E,IAA+C,IAAzBgG,EAAY7J,WAEpCoB,EAAUyI,EAAYN,YACtBnI,EAAUxG,EAAcwG,EAASc,GAAe,KAChDd,EAAUxG,EAAcwG,EAASe,GAAU,KAC3Cf,EAAUxG,EAAcwG,EAASgB,GAAa,KAC1CyH,EAAYN,cAAgBnI,IAC9BjH,EAAUyF,EAAUG,QAAS,CAAEzD,QAASuN,EAAY/I,cACpD+I,EAAYN,YAAcnI,IAK9BuI,GAAa,wBAAyBE,EAAa,OAE5C,IApBL3C,GAAa2C,IACN,EAoBV,EAWKQ,GAAoB,SAAUC,EAAOC,EAAQvN,GAEjD,GACEoH,KACY,OAAXmG,GAA8B,SAAXA,KACnBvN,KAAS6B,GAAY7B,KAAS0I,IAE/B,OAAO,EAOT,GACEhC,KACCF,GAAY+G,IACbrP,EAAWmH,GAAWkI,SAGjB,GAAI9G,IAAmBvI,EAAWoH,GAAWiI,SAG7C,IAAKzH,GAAayH,IAAW/G,GAAY+G,IAE5C,KAGCJ,GAAwBG,KACrBtH,GAAwBC,wBAAwB9H,QAChDD,EAAW8H,GAAwBC,aAAcqH,IAChDtH,GAAwBC,wBAAwB4C,UAC/C7C,GAAwBC,aAAaqH,MACvCtH,GAAwBK,8BAA8BlI,QACtDD,EAAW8H,GAAwBK,mBAAoBkH,IACtDvH,GAAwBK,8BAA8BwC,UACrD7C,GAAwBK,mBAAmBkH,KAGrC,OAAXA,GACCvH,GAAwBM,iCACtBN,GAAwBC,wBAAwB9H,QAChDD,EAAW8H,GAAwBC,aAAcjG,IAChDgG,GAAwBC,wBAAwB4C,UAC/C7C,GAAwBC,aAAajG,KAK3C,OAAO,OAGJ,GAAI8H,GAAoByF,SAIxB,GACLrP,EAAWwH,GAAgB9H,EAAcoC,EAAOwF,GAAiB,WAK5D,GACO,QAAX+H,GAA+B,eAAXA,GAAsC,SAAXA,GACtC,WAAVD,GACkC,IAAlCxP,EAAckC,EAAO,WACrB4H,GAAc0F,GAMT,GACL3G,KACCzI,EAAWqH,GAAmB3H,EAAcoC,EAAOwF,GAAiB,WAIhE,GAAIxF,EACT,OAAO,EAMT,OAAO,CACR,EAQKmN,GAA0B,SAAUrD,GACjC,MAAY,mBAAZA,GAAgCpM,EAAYoM,EAASrE,GAC7D,EAYK+H,GAAsB,SAAUX,GACpC,IAAIY,EACAzN,EACAuN,EACAlO,EAEJsN,GAAa,2BAA4BE,EAAa,MAEtD,IAAQL,EAAeK,EAAfL,WAGJ,GAACA,EAAD,CAIJ,IAAMkB,EAAY,CAChBC,SAAU,GACVC,UAAW,GACXC,UAAU,EACVC,kBAAmBhI,IAKd,IAHPzG,EAAImN,EAAW5N,OAGRS,KAAK,CAEV,IAAA0O,EADAN,EAAOjB,EAAWnN,GACVoL,EAARsD,EAAQtD,KAAMV,EAAdgE,EAAchE,aAYV,GAXJ/J,EAAiB,UAATyK,EAAmBgD,EAAKzN,MAAQhC,EAAWyP,EAAKzN,OACxDuN,EAASpO,GAAkBsL,GAG3BiD,EAAUC,SAAWJ,EACrBG,EAAUE,UAAY5N,EACtB0N,EAAUG,UAAW,EACrBH,EAAUM,mBAAgBrL,EAC1BgK,GAAa,wBAAyBE,EAAaa,GACnD1N,EAAQ0N,EAAUE,WAEdF,EAAUM,gBAKdxD,GAAiBC,EAAMoC,GAGlBa,EAAUG,UAKX,GAACjH,KAA4B1I,EAAW,OAAQ8B,GAAhD,CAMA6G,KACF7G,EAAQpC,EAAcoC,EAAOkF,GAAe,KAC5ClF,EAAQpC,EAAcoC,EAAOmF,GAAU,KACvCnF,EAAQpC,EAAcoC,EAAOoF,GAAa,MAI5C,IAAMkI,EAAQnO,GAAkB0N,EAAYP,UACxC,GAACe,GAAkBC,EAAOC,EAAQvN,GAAlC,CAgBJ,IATIqH,IAAoC,OAAXkG,GAA8B,SAAXA,IAE9C/C,GAAiBC,EAAMoC,GAGvB7M,EAAQsH,GAA8BtH,GAKtCsE,IACwB,WAAxBxC,EAAOF,IACkC,oBAAlCA,EAAaqM,iBAEpB,GAAIlE,QAGF,OAAQnI,EAAaqM,iBAAiBX,EAAOC,IAC3C,IAAK,cACHvN,EAAQsE,GAAmBhC,WAAWtC,GACtC,MAGF,IAAK,mBACHA,EAAQsE,GAAmB/B,gBAAgBvC,GAY/C,IACE+J,EACF8C,EAAYqB,eAAenE,EAAcU,EAAMzK,GAG/C6M,EAAY/B,aAAaL,EAAMzK,GAGjC/C,EAAS2F,EAAUG,QACpB,CAAC,MAAON,IAAI,CAlDZ,CAbA,MAFC+H,GAAiBC,EAAMoC,EAkE1B,CAGDF,GAAa,0BAA2BE,EAAa,KA5GpD,CA6GF,EAOKsB,GAAqB,SAArBA,EAA+BC,GACnC,IAAIC,EACEC,EAAiBxC,GAAgBsC,GAKvC,IAFAzB,GAAa,0BAA2ByB,EAAU,MAE1CC,EAAaC,EAAeC,YAElC5B,GAAa,yBAA0B0B,EAAY,MAG/CrB,GAAkBqB,KAKlBA,EAAWjK,mBAAmBjB,GAChCgL,EAAmBE,EAAWjK,SAIhCoJ,GAAoBa,IAItB1B,GAAa,yBAA0ByB,EAAU,KAClD,EA2SD,OAjSAxL,EAAU4L,SAAW,SAAUxD,GAAO,IAChCS,EACAgD,EACA5B,EACA6B,EACAC,EALgC5F,EAAUpK,UAAAC,OAAA,QAAA+D,IAAAhE,UAAA,GAAAA,UAAA,GAAJ,CAAC,EAevC,IANJyJ,IAAkB4C,KAEhBA,EAAQ,eAIW,kBAAVA,IAAuB0B,GAAQ1B,GAAQ,CAChD,GAA8B,oBAAnBA,EAAMvN,SAMT,MAAAY,EAAgB,8BAJtB,GAAqB,kBADrB2M,EAAQA,EAAMvN,YAEN,MAAAY,EAAgB,kCAK3B,CAGD,IAAKuE,EAAUK,YAAa,CAC1B,GACiC,WAA/BnB,EAAOJ,EAAOkN,eACiB,oBAAxBlN,EAAOkN,aACd,CACA,GAAqB,kBAAV5D,EACT,OAAOtJ,EAAOkN,aAAa5D,GAG7B,GAAI0B,GAAQ1B,GACV,OAAOtJ,EAAOkN,aAAa5D,EAAMV,UAEpC,CAED,OAAOU,CACR,CAeD,GAZKjE,IACH+B,GAAaC,GAIfnG,EAAUG,QAAU,GAGC,kBAAViI,IACTxD,IAAW,GAGTA,IAEE,GAAAwD,EAAMsB,SAAU,CAClB,IAAMxC,EAAU3K,GAAkB6L,EAAMsB,UACpC,IAAC3G,GAAamE,IAAYvD,GAAYuD,GAClC,MAAAzL,EACJ,0DAGL,OACI,GAAI2M,aAAiB3H,EAKI,KAD9BoL,GADAhD,EAAOV,GAAc,kBACD1G,cAAcQ,WAAWmG,GAAO,IACnChI,UAA4C,SAA1ByL,EAAanC,UAGX,SAA1BmC,EAAanC,SADtBb,EAAOgD,EAKPhD,EAAKoD,YAAYJ,OAEd,CAGH,IAACxH,KACAJ,KACAC,KAEuB,IAAxBkE,EAAMjN,QAAQ,KAEP,OAAAuG,IAAsB6C,GACzB7C,GAAmBhC,WAAW0I,GAC9BA,EAOF,KAHJS,EAAOV,GAAcC,IAIZ,OAAA/D,GAAa,KAAOE,GAAsB5C,GAAY,EAEhE,CAGGkH,GAAQzE,IACVkD,GAAauB,EAAKqD,YAOpB,IAHM,IAAAC,EAAejD,GAAgBtE,GAAWwD,EAAQS,GAGhDoB,EAAckC,EAAaR,YAEJ,IAAzB1B,EAAY7J,UAAkB6J,IAAgB6B,GAK9C1B,GAAkBH,KAKlBA,EAAYzI,mBAAmBjB,GACjCgL,GAAmBtB,EAAYzI,SAIjCoJ,GAAoBX,GAEpB6B,EAAU7B,GAMZ,GAHA6B,EAAU,KAGNlH,GACF,OAAOwD,EAIT,GAAI/D,GAAY,CACd,GAAIC,GAGK,IAFPyH,EAAahK,GAAuBkH,KAAKJ,EAAKpH,eAEvCoH,EAAKqD,YAEVH,EAAWE,YAAYpD,EAAKqD,iBAG9BH,EAAalD,EAcf,OAXI3F,GAAakJ,YAAclJ,GAAamJ,iBAQ1CN,EAAa9J,GAAWgH,KAAK3I,EAAkByL,GAAY,IAGtDA,CACR,CAEG,IAAAO,EAAiBpI,GAAiB2E,EAAKnB,UAAYmB,EAAKD,UAsBrD,OAlBL1E,IACAnB,GAAa,aACb8F,EAAKpH,eACLoH,EAAKpH,cAAc8K,SACnB1D,EAAKpH,cAAc8K,QAAQ1E,MAC3BvM,EAAWqD,GAA0BkK,EAAKpH,cAAc8K,QAAQ1E,QAEhEyE,EACE,aAAezD,EAAKpH,cAAc8K,QAAQ1E,KAAO,MAAQyE,GAIzDrI,KACFqI,EAAiBtR,EAAcsR,EAAgBhK,GAAe,KAC9DgK,EAAiBtR,EAAcsR,EAAgB/J,GAAU,KACzD+J,EAAiBtR,EAAcsR,EAAgB9J,GAAa,MAGvDd,IAAsB6C,GACzB7C,GAAmBhC,WAAW4M,GAC9BA,CACL,EAQDtM,EAAUwM,UAAY,SAAUrG,GAC9BD,GAAaC,GACbhC,IAAa,CACd,EAODnE,EAAUyM,YAAc,WACtB5G,GAAS,KACT1B,IAAa,CACd,EAYDnE,EAAU0M,iBAAmB,SAAUC,EAAK9B,EAAMzN,GAE3CyI,IACHK,GAAa,CAAC,GAGhB,IAAMwE,EAAQnO,GAAkBoQ,GAC1BhC,EAASpO,GAAkBsO,GACjC,OAAOJ,GAAkBC,EAAOC,EAAQvN,EACzC,EASD4C,EAAU4M,QAAU,SAAU5C,EAAY6C,GACZ,oBAAjBA,IAIX1K,GAAM6H,GAAc7H,GAAM6H,IAAe,GACzCzP,EAAU4H,GAAM6H,GAAa6C,GAC9B,EAUD7M,EAAU8M,WAAa,SAAU9C,GAC/B,GAAI7H,GAAM6H,GACR,OAAO3P,EAAS8H,GAAM6H,GAEzB,EAQDhK,EAAU+M,YAAc,SAAU/C,GAC5B7H,GAAM6H,KACR7H,GAAM6H,GAAc,GAEvB,EAODhK,EAAUgN,eAAiB,WACzB7K,GAAQ,CAAC,CACV,EAEMnC,CACR,C,OAEcF,I", "file": "static/js/4.ff9b16c9.chunk.js", "sourcesContent": ["const {\n  hasOwnProperty,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\nexport function unapply(func) {\n  return (thisArg, ...args) => apply(func, thisArg, args);\n}\n\nexport function unconstruct(func) {\n  return (...args) => construct(func, args);\n}\n\n/* Add properties to a lookup table */\nexport function addToSet(set, array, transformCaseFunc) {\n  transformCaseFunc = transformCaseFunc ?? stringToLowerCase;\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/* Shallow clone an object */\nexport function clone(object) {\n  const newObject = create(null);\n\n  let property;\n  for (property in object) {\n    if (apply(hasOwnProperty, object, [property]) === true) {\n      newObject[property] = object[property];\n    }\n  }\n\n  return newObject;\n}\n\n/* IE10 doesn't support __lookupGetter__ so lets'\n * simulate it. It also automatically checks\n * if the prop is function or getter and behaves\n * accordingly. */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(element) {\n    console.warn('fallback value for', element);\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  // Object\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  hasOwnProperty,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n]);\n\n// SVG\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n]);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n]);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'fedropshadow',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n]);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n]);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n]);\n\nexport const text = freeze(['#text']);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'xmlns',\n  'slot',\n]);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n]);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n]);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\nexport const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n", "import * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  freeze,\n  arrayForEach,\n  arrayPop,\n  arrayPush,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n} from './utils.js';\n\nconst getGlobal = () => (typeof window === 'undefined' ? null : window);\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */\nconst _createTrustedTypesPolicy = function (trustedTypes, document) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (\n    document.currentScript &&\n    document.currentScript.hasAttribute(ATTR_NAME)\n  ) {\n    suffix = document.currentScript.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nfunction createDOMPurify(window = getGlobal()) {\n  const DOMPurify = (root) => createDOMPurify(root);\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = VERSION;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  const originalDocument = window.document;\n\n  let { document } = window;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  const trustedTypesPolicy = _createTrustedTypesPolicy(\n    trustedTypes,\n    originalDocument\n  );\n  const emptyHTML = trustedTypesPolicy ? trustedTypesPolicy.createHTML('') : '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let documentMode = {};\n  try {\n    documentMode = clone(document).documentMode ? document.documentMode : {};\n  } catch (_) {}\n\n  let hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined &&\n    documentMode !== 9;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    Object.create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? (PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE)\n        : (PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE);\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS =\n      'ALLOWED_TAGS' in cfg\n        ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n        : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR =\n      'ALLOWED_ATTR' in cfg\n        ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n        : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES =\n      'ALLOWED_NAMESPACES' in cfg\n        ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n        : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES =\n      'ADD_URI_SAFE_ATTR' in cfg\n        ? addToSet(\n            clone(DEFAULT_URI_SAFE_ATTRIBUTES), // eslint-disable-line indent\n            cfg.ADD_URI_SAFE_ATTR, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS =\n      'ADD_DATA_URI_TAGS' in cfg\n        ? addToSet(\n            clone(DEFAULT_DATA_URI_TAGS), // eslint-disable-line indent\n            cfg.ADD_DATA_URI_TAGS, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS =\n      'FORBID_CONTENTS' in cfg\n        ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n        : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS =\n      'FORBID_TAGS' in cfg\n        ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n        : {};\n    FORBID_ATTR =\n      'FORBID_ATTR' in cfg\n        ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n        : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...TAGS.text]);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  const MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  const HTML_INTEGRATION_POINTS = addToSet({}, [\n    'foreignobject',\n    'desc',\n    'title',\n    'annotation-xml',\n  ]);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, TAGS.svg);\n  addToSet(ALL_SVG_TAGS, TAGS.svgFilters);\n  addToSet(ALL_SVG_TAGS, TAGS.svgDisallowed);\n\n  const ALL_MATHML_TAGS = addToSet({}, TAGS.mathMl);\n  addToSet(ALL_MATHML_TAGS, TAGS.mathMlDisallowed);\n\n  /**\n   *\n   *\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element) {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  const _forceRemove = function (node) {\n    arrayPush(DOMPurify.removed, { element: node });\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      node.parentNode.removeChild(node);\n    } catch (_) {\n      try {\n        node.outerHTML = emptyHTML;\n      } catch (_) {\n        node.remove();\n      }\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  const _removeAttribute = function (name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node,\n      });\n    }\n\n    node.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\"\" attributes\n    if (name === 'is' && !ALLOWED_ATTR[name]) {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(node);\n        } catch (_) {}\n      } else {\n        try {\n          node.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty) {\n    /* Create a HTML document */\n    let doc;\n    let leadingWhitespace;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n  const _createIterator = function (root) {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT |\n        NodeFilter.SHOW_COMMENT |\n        NodeFilter.SHOW_TEXT |\n        NodeFilter.SHOW_PROCESSING_INSTRUCTION |\n        NodeFilter.SHOW_CDATA_SECTION,\n      null,\n      false\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  const _isClobbered = function (elm) {\n    return (\n      elm instanceof HTMLFormElement &&\n      (typeof elm.nodeName !== 'string' ||\n        typeof elm.textContent !== 'string' ||\n        typeof elm.removeChild !== 'function' ||\n        !(elm.attributes instanceof NamedNodeMap) ||\n        typeof elm.removeAttribute !== 'function' ||\n        typeof elm.setAttribute !== 'function' ||\n        typeof elm.namespaceURI !== 'string' ||\n        typeof elm.insertBefore !== 'function' ||\n        typeof elm.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  const _isNode = function (object) {\n    return typeof Node === 'object'\n      ? object instanceof Node\n      : object &&\n          typeof object === 'object' &&\n          typeof object.nodeType === 'number' &&\n          typeof object.nodeName === 'string';\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  const _executeHook = function (entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    arrayForEach(hooks[entryPoint], (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode) {\n    let content;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check if tagname contains Unicode */\n    if (regExpTest(/[\\u0080-\\uFFFF]/, currentNode.nodeName)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      (!_isNode(currentNode.content) ||\n        !_isNode(currentNode.content.firstElementChild)) &&\n      regExpTest(/<[/\\w]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Mitigate a problem with templates inside select */\n    if (\n      tagName === 'select' &&\n      regExpTest(/<template/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any ocurrence of processing instructions */\n    if (currentNode.nodeType === 7) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        )\n          return false;\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        )\n          return false;\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            parentNode.insertBefore(\n              cloneNode(childNodes[i], true),\n              getNextSibling(currentNode)\n            );\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = stringReplace(content, MUSTACHE_EXPR, ' ');\n      content = stringReplace(content, ERB_EXPR, ' ');\n      content = stringReplace(content, TMPLIT_EXPR, ' ');\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_basicCustomElementTest(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _basicCustomElementCheck\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   * @param {string} tagName name of the tag of the node to sanitize\n   */\n  const _basicCustomElementTest = function (tagName) {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode) {\n    let attr;\n    let value;\n    let lcName;\n    let l;\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n    };\n    l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      attr = attributes[l];\n      const { name, namespaceURI } = attr;\n      value = name === 'value' ? attr.value : stringTrim(attr.value);\n      lcName = transformCaseFunc(name);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Remove attribute */\n      _removeAttribute(name, currentNode);\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        value = stringReplace(value, MUSTACHE_EXPR, ' ');\n        value = stringReplace(value, ERB_EXPR, ' ');\n        value = stringReplace(value, TMPLIT_EXPR, ' ');\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        arrayPop(DOMPurify.removed);\n      } catch (_) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment) {\n    let shadowNode;\n    const shadowIterator = _createIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(shadowNode)) {\n        continue;\n      }\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(shadowNode);\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body;\n    let importedNode;\n    let currentNode;\n    let oldNode;\n    let returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Check we can run. Otherwise fall back or ignore */\n    if (!DOMPurify.isSupported) {\n      if (\n        typeof window.toStaticHTML === 'object' ||\n        typeof window.toStaticHTML === 'function'\n      ) {\n        if (typeof dirty === 'string') {\n          return window.toStaticHTML(dirty);\n        }\n\n        if (_isNode(dirty)) {\n          return window.toStaticHTML(dirty.outerHTML);\n        }\n      }\n\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Fix IE's strange behavior with manipulated textNodes #89 */\n      if (currentNode.nodeType === 3 && currentNode === oldNode) {\n        continue;\n      }\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(currentNode)) {\n        continue;\n      }\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(currentNode);\n\n      oldNode = currentNode;\n    }\n\n    oldNode = null;\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmod) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, ERB_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, TMPLIT_EXPR, ' ');\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function (cfg) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   * @return {Function} removed(popped) hook\n   */\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      return arrayPop(hooks[entryPoint]);\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n"], "sourceRoot": ""}
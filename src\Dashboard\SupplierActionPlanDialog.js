import React, { useState, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { Calendar } from 'primereact/calendar';
import { But<PERSON> } from 'primereact/button';
import { InputTextarea } from 'primereact/inputtextarea';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { format } from 'date-fns';
import APIServices from '../service/APIService';
import { API } from '../constants/api_url';
import Swal from 'sweetalert2';
import { DateTime } from 'luxon';
import { useSelector } from 'react-redux';
import moment from 'moment';

// Mapping array for Supplier Category
const categoryList = [
    { name: 'Forging & Machining', value: 1 },
    { name: 'Casting & Machining', value: 2 },
    { name: 'Pressing & Fabrication', value: 3 },
    { name: 'Proprietary Mechanical', value: 4 },
    { name: 'Proprietary Electrical', value: 5 },
    { name: 'Plastics, Rubber, Painting and Stickers', value: 6 },
    { name: 'EV/3W/2W', value: 7 },
    { name: 'BW', value: 8 },
    { name: 'Accessories', value: 9 },
    { name: 'IDM (Indirect Material)', value: 10 },
    { name: 'Import', value: 11 }
];

export default function SupplierActionPlanDialog({ visible, onHide, data, refresh }) {
    const [actionData, setActionData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [comments, setComments] = useState([]);
    const login_data = useSelector((state) => state.user.userdetail);
    const admin_data = useSelector((state) => state.user.admindetail);
    const userList = useSelector((state) => state.userlist.userList);

    useEffect(() => {
        if (data?.actions?.length) {
            const initialized = data.actions.map(action => ({
                ...action,
                rootCause: action.rootCause || '',
                proposedCorrectiveAction: action.proposedCorrectiveAction || '',
                actionTargetDate: action.actionTargetDate ? new Date(action.actionTargetDate) : null,
                actionPlanApproverComments: action.actionPlanApproverComments || ''
            }));
            setActionData(initialized);

            // Initialize comments array with empty strings for each action
            setComments(new Array(data.actions.length).fill(''));
        }
    }, [data]);
    const userLookup = userList.reduce((acc, user) => {
        acc[user.id] = user;
        return acc;
    }, {});
    const getUser = (id) => {
        if (id === admin_data.id) {
            return "Enterprise Admin";
        }
        console.log(userLookup[id])
        return userLookup[id] ? userLookup[id].information.empname : ''
    };
    const isSaveEnabled = actionData.every(
        act =>
            act.rootCause?.trim() &&
            act.proposedCorrectiveAction?.trim() &&
            act.actionTargetDate
    );

    const handleChange = (index, field, value) => {
        const updated = [...actionData];
        updated[index][field] = value;
        setActionData(updated);
    };

    const handleSave = () => {
        if (isSaveEnabled) {
            setLoading(true);
            console.log(actionData);
            APIServices.post(API.SubmitActionPlan, actionData)
                .then(() => {
                    setLoading(false);
                    Swal.fire({
                        icon: 'success',
                        title: 'Action Plan Submitted',
                        text: 'Your action plan has been submitted successfully.'
                    });
                    refresh();
                    onHide();
                })
                .catch(error => {
                    setLoading(false);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to submit action plan. Please try again.'
                    });
                    console.error(error);
                });
        }
    };

    const handleCommentChange = (index, value) => {
        const newComments = [...comments];
        newComments[index] = value;
        setComments(newComments);

        // Update the actionData with the new comment
        const updatedActionData = [...actionData];
        updatedActionData[index].actionPlanApproverComments = value;
        setActionData(updatedActionData);
    };

    const validateComments = () => {
        // For return action, at least one action should have a comment
        return comments.some(comment => comment.trim() !== '');
    };

    const handleApprove = () => {
        setLoading(true);

        // Update each action with approval properties
        const updatedActions = actionData.map(action => ({
            ...action,
            actionPlanApprovedBy: login_data.id,
            actionPlanApprovedOn: DateTime.utc().toISO(),
            actionPlanApproverComments: action.actionPlanApproverComments || ''
        }));



        APIServices.post(API.ApproveActionPlan, updatedActions)
            .then(() => {
                setLoading(false);
                Swal.fire({
                    icon: 'success',
                    title: 'Action Plan Approved',
                    text: 'The action plan has been approved successfully.'
                });
                refresh();
                onHide();
            })
            .catch(error => {
                setLoading(false);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to approve action plan. Please try again.'
                });
                console.error(error);
            });
    };

    const handleReturn = () => {
        // Validate that at least one action has a comment
        if (!validateComments()) {
            Swal.fire({
                icon: 'error',
                title: 'Comments Required',
                text: 'Please provide comments for at least one action before returning.'
            });
            return;
        }

        // Update each action with return properties
        const updatedActions = actionData.map(action => ({
            ...action,
            actionPlanRejectedBy: login_data.id,
            actionPlanRejectedOn: DateTime.utc().toISO()
        }));

        // Create payload with updated actions and action plan type
        const payload = {
            actions: updatedActions,
            actionPlanId: data.id,
            actionPlanType: 21 // Keep as 21 for returned action plans
        };

        setLoading(true);
        APIServices.post(API.RejectActionPlan, updatedActions)
            .then(() => {
                setLoading(false);
                Swal.fire({
                    icon: 'success',
                    title: 'Action Plan Returned',
                    text: 'The action plan has been returned with feedback.'
                });
                refresh();
                onHide();
            })
            .catch(error => {
                setLoading(false);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to return action plan. Please try again.'
                });
                console.error(error);
            });
    };

    // Function to render supplier metadata
    const renderSupplierMetadata = () => {
        if (!data || !data.vendor) return null;

        const vendor = data.vendor;

        // Map supplier category value to name
        const getSupplierCategoryName = (categoryValue) => {
            const category = categoryList.find(item => item.value === categoryValue);
            return category ? category.name : categoryValue;
        };

        // Get calibration team members
        const getCalibrationTeamMembers = () => {
            const allAssessors = [
                ...(data.group1?.assessors || []),
                ...(data.group2?.assessors || []),
                ...(data.group3?.assessors || []),
                ...(data.group4?.assessors || [])
            ];

            const uniqueAssessors = [...new Set(allAssessors)];
            return uniqueAssessors.map(assessorId => {
                const user = userLookup[assessorId];
                return user ? user.information?.empname || 'Unknown' : 'Unknown';
            }).join(', ') || 'Not Assigned';
        };

        return (
            <Card className="mb-4 shadow-sm">
                <div className="row">
                    <div className="col-md-6">
                        <h4 className="mb-3 clr-navy">Supplier Information</h4>
                        <Divider />
                        <div className="row">
                            <div className="col-md-4">
                                <p><strong>Supplier Name:</strong> {vendor.supplierName || 'N/A'}</p>
                                <p><strong>Supplier Code:</strong> {vendor.code || 'N/A'}</p>
                                <p><strong>Supplier Category:</strong> {getSupplierCategoryName(vendor.supplierCategory) || 'N/A'}</p>
                                <p><strong>Supplier Location:</strong> {vendor.supplierLocation || 'N/A'}</p>
                            </div>
                            <div className="col-md-4">
                                <p><strong>Supplier SPOC:</strong> {vendor.supplierSPOC || 'N/A'}</p>
                                <p><strong>Contact:</strong> {vendor.supplierContact || 'N/A'}</p>
                                <p><strong>Contact 2:</strong> {vendor.supplierContact2 || 'N/A'}</p>
                                <p><strong>Contact 3:</strong> {vendor.supplierContact3 || 'N/A'}</p>
                            </div>
                            <div className="col-md-4">
                                <p><strong>Email:</strong> {vendor.supplierEmail || 'N/A'}</p>
                                <p><strong>Email 2:</strong> {vendor.supplierEmail2 || 'N/A'}</p>
                                <p><strong>Email 3:</strong> {vendor.supplierEmail3 || 'N/A'}</p>
                                <p><strong>Spent On:</strong> {vendor.supplierSpentOn ? `${vendor.supplierSpentOn}%` : 'N/A'}</p>
                                <p><strong>Modified On:</strong> {vendor.modified_on ? moment(vendor.modified_on).format('DD-MM-YYYY') : 'N/A'}</p>
                            </div>
                        </div>
                    </div>
                    <div className="col-md-6">
                        <h4 className="mb-3 clr-navy">MSI Assessment Information</h4>
                        <Divider />
                        <div className="row">
                            <div className="col-md-4">
                                <p><strong>Self-assessment Due Date:</strong> {data.assessmentEndDate ? moment(data.assessmentEndDate).format('DD MMM YYYY') : 'N/A'}</p>
                                <p><strong>Self-assessment Submitted Date:</strong> {data.supplierAssignmentSubmission?.submitted_on ? moment(data.supplierAssignmentSubmission.submitted_on).format('DD MMM YYYY') : 'Not Submitted'}</p>
                                <p><strong>MSI Self-Assessment Score:</strong> {data.supplierAssignmentSubmission?.supplierMSIScore || '-'}</p>
                            </div>
                            <div className="col-md-4">
                                <p><strong>Audit Start Date:</strong> {data.auditStartDate ? moment(data.auditStartDate).format('DD MMM YYYY') : 'N/A'}</p>
                                <p><strong>Audit End Date:</strong> {data.auditEndDate ? moment(data.auditEndDate).format('DD MMM YYYY') : 'N/A'}</p>
                                <p><strong>MSI Audit Score:</strong> {data.auditorAssignmentSubmission?.auditorMSIScore || '-'}</p>
                            </div>
                            <div className="col-md-4">
                                <p><strong>Calibration Team Members:</strong> {getCalibrationTeamMembers()}</p>
                                <p><strong>Assessment Status:</strong> {data.supplierAssignmentSubmission?.type === 1 ? 'Submitted' : data.supplierAssignmentSubmission?.type === 2 ? 'Approved' : 'Not Started'}</p>
                                <p><strong>Audit Status:</strong> {data.auditorAssignmentSubmission?.type === 1 ? 'Submitted' : data.auditorAssignmentSubmission?.type === 2 ? 'Approved' : 'Not Started'}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </Card>
        );
    };

    return (
        <Dialog header="Action Details" visible={visible} onHide={onHide} style={{ width: '80vw' }}>
            <div className="p-3">
                <div className="mb-3">
                    <strong>MSI ID:</strong> {data?.msiId} <br />
                    <strong>{data?.statusCode === 6 ? 'Submitted on' : 'Approved on'}:</strong> {data?.submittedDate ? DateTime.fromISO(data?.submittedDate, { zone: 'utc' }).toLocal().toFormat('dd-LLL-yyyy') : 'N/A'} <br />
                    {data?.statusCode === 0 && (
                        <div className="mt-1">
                            <span className="badge badge-success p-1">
                                <i className="pi pi-check-circle mr-1"></i> Approved
                            </span>
                        </div>
                    )}
                </div>

                {/* Supplier Metadata */}
                {renderSupplierMetadata()}

                <Accordion multiple activeIndex={[0]}>
                    {actionData.map((action, index) => (
                        <AccordionTab key={index} header={`#${action.actionId} - ${action.finding}`}>
                            <div className="mb-3">
                                <label><strong>Root Cause:</strong></label>
                                <div dangerouslySetInnerHTML={{ __html: action.rootCause }} />
                            </div>

                            <div className="mb-3">
                                <label><strong>Proposed Corrective Action(s):</strong></label>
                                <div dangerouslySetInnerHTML={{ __html: action.proposedCorrectiveAction }} />
                            </div>

                            <div className="mb-3">
                                <label><strong>Target Completion Date:</strong></label><br />
                                <div>{action.actionTargetDate ? format(new Date(action.actionTargetDate), 'dd-LLL-yyyy') : 'N/A'}</div>
                            </div>

                            {/* For active action plans that haven't been approved/rejected yet, show editable textarea */}
                            {(data?.statusCode === 6 && !action.actionPlanApprovedOn && !action.actionPlanRejectedOn) && (
                                <div >
                                    <label style={{ marginBottom: 10 }}><strong>Comments:</strong></label><br />
                                    <InputTextarea
                                        value={action.actionPlanApproverComments}
                                        onChange={(e) => handleCommentChange(index, e.target.value)}
                                        rows={3}
                                        style={{ width: '100%' }}
                                        placeholder="Enter your comments here..."
                                    />
                                </div>
                            )}

                            {/* Only show comments section if comments exist */}
                            {((data?.statusCode === 0 || action.actionPlanApprovedOn || action.actionPlanRejectedOn) && action.actionPlanApproverComments) && (
                                <div >
                                    <label classname='flex' style={{ marginBottom: 10 }}><strong>Comments:</strong></label><br />
                                    <div className="p-3">
                                        <div>{action.actionPlanApproverComments}</div>
                                    </div>
                                </div>
                            )}


                        </AccordionTab>
                    ))}
                </Accordion>

                <div className="mt-4 text-right">
                    {actionData.every((x) => x.type === 12) && isSaveEnabled && (
                        <Button label="Save & Submit Action Plan" icon="pi pi-check" onClick={handleSave} className="mr-2" loading={loading} />
                    )}

                    {/* Show approve/return buttons only for action plans that are under review (statusCode = 6) */}
                    {data?.statusCode === 6 && !actionData.some(action => action.actionPlanApprovedOn || action.actionPlanRejectedOn) && (
                        <>
                            <Button
                                label="Approve"
                                icon="pi pi-check-circle"
                                className="p-button-success mr-2"
                                onClick={handleApprove}
                                loading={loading}
                            />
                            <Button
                                label="Return"
                                icon="pi pi-times-circle"
                                className="p-button-danger"
                                onClick={handleReturn}
                                loading={loading}
                                disabled={!validateComments()}
                            />
                        </>
                    )}

                    {/* Show status message for approved action plans (statusCode = 0) */}
                    {data?.statusCode === 0 && (
                        <div className="text-success">
                            <i className="pi pi-check-circle mr-2"></i>
                            This action plan has been approved
                        </div>
                    )}

                    {/* Show status messages based on action properties */}
                    {data?.statusCode === 6 && actionData.some(action => action.actionPlanApprovedOn) && (
                        <div className="text-success">
                            <i className="pi pi-check-circle mr-2"></i>
                            This action plan has been approved
                        </div>
                    )}

                    {data?.statusCode === 6 && actionData.some(action => action.actionPlanRejectedOn) && (
                        <div className="text-danger">
                            <i className="pi pi-times-circle mr-2"></i>
                            This action plan has been returned
                        </div>
                    )}
                </div>
            </div>
        </Dialog>
    );
}

import React, { useEffect, useState } from "react";
import { useLocation, useHistory, useParams } from "react-router-dom/cjs/react-router-dom";
import { motion } from "framer-motion";
import useForceUpdate from "use-force-update";

import { DateTime } from "luxon";
import moment from "moment";
import { API } from "../constants/api_url";
import APIServices from "../service/APIService";
import { useSelector } from "react-redux";
import { Dropdown } from "primereact/dropdown";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Tag } from "primereact/tag";
import { InputText } from "primereact/inputtext";


const SupplierHome = () => {

    const { id } = useParams();
    const navigate = useHistory()
    const [searchstr, setSearchStr] = useState('')
    const [data, setData] = useState([])
    const [rawrf, setRawRF] = useState([])
    const [filterdcf, setFilterDCF] = useState([])
    const [done, setDone] = useState(false)
    const supplierList = useSelector(state => state.userlist.supplierList)
    const [ass, setAss] = useState([])
    const frequency_list = [{ name: 'Monthly', id: 1 }, { name: 'Bi-Monthly', id: 2 }, { name: 'Quartely', id: 3 }, { name: 'Annually', id: 4 }, { name: 'Bi-Annually', id: 5 }, { name: 'Undefined', id: 6 }]
    const [overallmetric, setOverallMetric] = useState([])
    const [list, setList] = useState([])
    const [update, setUpdate] = useState(false)
    const [sitelist, setSiteList] = useState([])
    const login_data = useSelector((state) => state.user.userdetail)
    const client_info = useSelector((state) => state.userlist.admindetail)
    const [rfass, setRFAss] = useState([])
    const [present, setPresent] = useState([])
    const [past, setPast] = useState([])
    const [pastbk, setPastBK] = useState([])

    const [show, setShow] = useState({ entry: false, approver: false, loaded: false })
    const [selectedsite, setSelectedSite] = useState(0)

    const [future, setFuture] = useState([])
    const [dcflist, setDcfList] = useState([])

    const [sitelistall, setSiteListAll] = useState([])
    const [dcfassign, setDCFAssign] = useState([])
    const [submitteddcf, setSubmittedDCF] = useState([])
    const [pendingap, setPendingAP] = useState([])
    const [response, setResponse] = useState([])
    const [rfresponse, setRFResponse] = useState([])

    const forceUpdate = useForceUpdate()

    useEffect(() => {

        if (Object.keys(client_info).length !== 0 && done === false) {

            setDone(true)
            console.log('its working')
            let uriString = {
                "include": [{ "relation": "locationTwos", "scope": { "include": [{ "relation": "locationThrees" }] } }]

            }
            let uriString3 = {
                "include": [{ "relation": "newDataPoints" }]

            }
            let uriString2 = {
                "include": [{ "relation": "newTopics", "scope": { "include": [{ "relation": "newMetrics", "scope": { "include": [{ "relation": "newDataPoints" }] } }] } }]


            }

            APIServices.get(API.AssignSRFUser_UP(login_data.information.cid)).then((res) => {

                setAss(res.data)
            })
            let srf_list = [], srf_submitted = [], locloc = []
            let site_url = API.LocationOne_UP(login_data.information.cid) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
            const promise1 = APIServices.get(API.SRF)
            const promise2 = APIServices.get(API.SRF_Submit_UP(login_data.information.cid))
            const promise3 = APIServices.get(API.LocationThree)
            const promise4 = APIServices.get(API.AssignDCFClient_UP(login_data.information.cid))
            Promise.all([promise1, promise2, promise3, promise4]).then(function (values) {

                let filter_dcf = []

                if (values[0].data.length !== 0 && values[3].data.length !== 0) {
                    filter_dcf = values[0].data.filter(i => values[3].data[0].cf_ids.includes(i.id));
                    srf_list = values[0].data
                    setDcfList(values[0].data)
                }


                setFilterDCF(filter_dcf)

                srf_submitted = values[1].data;
                locloc = values[2].data
                setSubmittedDCF((prev) => ([...prev, ...values[1].data]))
                forceUpdate()

                APIServices.get(API.AssignSRFUser_UP(login_data.information.cid)).then((res) => {
                    setDCFAssign(JSON.parse(JSON.stringify(res.data)))
                    let filter = [], locationFilter = [0], rawData = [], pending = [], reporter = false, reviewer = false



                    res.data.forEach((item) => {

                        if (item.type === 0 && item.user_id !== login_data.id && item.reviewer_id === login_data.id && filter_dcf.findIndex(i => { return i.id === item.srfId }) !== -1) {
                            reviewer = true
                            if (srf_list.filter((k) => { return k.id === item.srfId }).length !== 0 && locloc.findIndex((k) => { return k.id === item.site }) !== -1) {
                                console.log(srf_submitted.filter((k) => { return k.cf === item.srfId && k.site === item.site && (k.type === 1 || k.type === 2 || k.type === 3 || k.reject === 1) }))
                                if (srf_submitted.filter((k) => { return k.cf === item.srfId && k.site === item.site && (k.type === 1 || k.type === 2 || k.type === 3 || k.reject === 1) }).length !== 0) {

                                    if (pending.findIndex((k) => { return k.id === srf_submitted.filter((k) => { return k.cf === item.srfId && k.site === item.site && (k.type === 1 || k.type === 2 || k.type === 3 || k.reject === 1) })[0].id }) === -1) {
                                        pending.push(...srf_submitted.filter((k) => { return k.cf === item.srfId && k.site === item.site && (k.type === 1 || k.type === 2 || k.type === 3 || k.reject === 1) }))
                                    }
                                    if (locationFilter.findIndex((k) => { return k === item.site }) === -1) {
                                        locationFilter.push(item.site)
                                    }
                                }
                            }
                        }
                        console.log(item, filter_dcf, login_data.id)
                        if (item.type === 0 && item.user_id === login_data.id && filter_dcf.findIndex(i => { return i.id === item.srfId }) !== -1) {
                            reporter = true
                            if (srf_list.filter((k) => { return k.id === item.srfId }).length !== 0 && locloc.findIndex((k) => { return k.id === item.site }) !== -1) {

                                item.srf_ = srf_list.filter((k) => { return k.id === item.srfId })[0]
                                item.frequency_ = frequency_list.filter((k) => { return k.id === item.frequency })[0]


                                filter.push(item)
                                if (locationFilter.findIndex((k) => { return k === item.site }) === -1) {

                                    locationFilter.push(item.site)
                                }
                                console.log(DateTime.fromISO(item.start_date, { zone: 'utc' }), DateTime.local(), 'dcf')
                                let st_date = DateTime.fromISO(item.start_date, { zone: 'utc' }).toLocal(), ed_date = DateTime.utc().toLocal()
                                if (typeof item.end_date === 'string') {
                                    ed_date = DateTime.fromISO(item.end_date, { zone: 'utc' }).toLocal()
                                }


                                getMonthsLuxon(st_date, ed_date, item.frequency, item, srf_submitted)

                                rawData.push(item)
                            }

                        }
                    })
                    console.log(pending)
                    setShow((prev) => ({ ...prev, 'approver': reviewer, 'entry': reporter, loaded: true }))
                    // setShow((prev) => ({ ...prev, 'approver': true,'entry':true, loaded: true }))
                    setPendingAP(pending)
                    setData(rawData)
                    APIServices.get(site_url).then((rest) => {
                        let site_list = [{ name: 'All', id: 0 }]
                        let site_list_all = []
                        rest.data.forEach((country) => {
                            if (country.locationTwos !== undefined) {
                                country.locationTwos.forEach((city) => {
                                    if (city.locationThrees !== undefined) {
                                        city.locationThrees.forEach((site) => {
                                            filter.forEach((item) => { if (site.id === item.site) { item.site = site } })
                                            site_list.push({ name: site.name + ' (' + city.name + ')', id: site.id, country: { id: country.id, name: country.name }, city: { id: city.id, name: city.name } })
                                            site_list_all.push({ name: site.name + ' (' + city.name + ')', id: site.id, country: { id: country.id, name: country.name }, city: { id: city.id, name: city.name } })

                                        })
                                    }
                                })
                            }
                        })

                        setSiteList(site_list.filter((k) => { return locationFilter.includes(k.id) }))

                    })

                })

            })
        }
    }, [client_info])
    useEffect(() => {

        if (past.length === 0 && selectedsite !== null) {
            console.log(selectedsite)
            let loc = JSON.parse(JSON.stringify(data))
            loc.forEach((i, j) => {
                if (i.site.id === selectedsite || selectedsite === 0) {
                    i.site = i.site.id
                    let ed_date = DateTime.utc().toLocal()
                    let st_date = DateTime.fromISO(i.start_date, { zone: 'utc' }).toLocal()
                    if (typeof i.end_date === 'string') {
                        ed_date = DateTime.fromISO(i.end_date, { zone: 'utc' }).toLocal()
                    }

                    getMonthsLuxon(st_date, ed_date, i.frequency, i, submitteddcf)


                }
            })
            forceUpdate()
        }
    }, [selectedsite])
    const verticalText = (str) => {
        let div = document.createElement('div')
        div.style.position = 'absolute'
        div.style.right = '10px'
        div.style.top = '1%'
        div.style.flexDirection = 'column'
        div.style.opacity = 0.5
        div.className = 'flex'

        for (var i = 0; i < str.trim().length; i++) {
            let charc = str.trim()
            let label = document.createElement('label')
            label.innerHTML = charc[i]
            label.style = "color: lightgray; font-weight: 900; font-size: 18px;text-shadow: 1px 2px gray;"
            div.append(label)
        }

        return div.outerHTML
    }
    const getMonthsLuxon = (startDate, endDate, frequency, item, old) => {

        var betweenMonths = [];
        let type = 0, past_ = [], present_ = [], future_ = []

        past_ = JSON.parse(JSON.stringify(past))
        present_ = JSON.parse(JSON.stringify(present))
        future_ = JSON.parse(JSON.stringify(future))

        if (Math.round(startDate.diff(DateTime.utc().startOf('month'), 'days').days).toFixed(0) === 0) {
            let endDate_ = startDate

            let endDate__ = DateTime.utc().toLocal()
            if (frequency === 1) {

                let check = checkSubmission(item.srf_, item.site, endDate__.toFormat('LLL-yyyy'), old)

                if (check.result) {

                    future_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: endDate__.toFormat('LLL-yyyy'), duedate: endDate__.plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: 0, site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })

                } else {
                    if (check.data) {

                        future_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: endDate__.toFormat('LLL-yyyy'), duedate: endDate__.plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: 0, site: item.site, company_id: login_data.information.cid, frequency })
                    }

                }
            } else if (frequency === 6) {
                let check = checkSubmission(item.srf_, item.site, endDate__.toFormat('LLL-yyyy'), old)

                if (check.result) {


                    past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: endDate__.toFormat('LLL-yyyy'), duedate: endDate__.toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(endDate__, 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })
                } else {
                    if (check.data) {

                        past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: endDate__.toFormat('LLL-yyyy'), duedate: endDate__.toFormat('LLL-yyyy'), overdue: 0, site: item.site, type: 6, company_id: login_data.information.cid, frequency })
                    }

                }


            }

        }
        else if (Math.round(startDate.diff(DateTime.utc().startOf('month'), 'days').days) < 31) {
            var date = startDate
            console.log(startDate.diff(endDate.startOf('month'), 'days').days)


            let endDate__ = DateTime.utc().toLocal()
            while (date <= endDate) {

                betweenMonths.push(date.toFormat('LLL-yyyy'));
                date = date.plus({ months: 1 })

            }

            if (frequency === 1) {
                console.log(splitArray(betweenMonths, 1), 'SP')
                splitArray(betweenMonths, 1).forEach((months, ind) => {
                    console.log(endDate__.month, DateTime.fromFormat(months[0], 'LLL-yyyy').month, endDate__.year, DateTime.fromFormat(months[0], 'LLL-yyyy').year, item.srf_, ind, months[0])
                    if (endDate__.month === DateTime.fromFormat(months[0], 'LLL-yyyy').month && endDate__.year === DateTime.fromFormat(months[0], 'LLL-yyyy').year) {
                        let check = checkSubmission(item.srf_, item.site, months[0], old)

                        if (check.result) {


                            future_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0], duedate: DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })
                        } else {
                            if (check.data) {
                                future_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0], duedate: DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                            }

                        }


                    } else {

                        if (Math.round(endDate__.diff(DateTime.fromFormat(months[0], 'LLL-yyyy'), 'months').months) === 1) {


                            let check = checkSubmission(item.srf_, item.site, months[0], old)


                            if (check.result) {


                                past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0], duedate: DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })
                            } else {
                                if (check.data) {
                                    past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0], duedate: DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                                }

                            }


                        } else {
                            let check = checkSubmission(item.srf_, item.site, months[0], old)
                            console.log(check, months[0])
                            if (check.result) {


                                past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0], duedate: DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })
                            } else {
                                if (check.data) {

                                    past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0], duedate: DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                                }

                            }
                        }
                    }

                })
            } else if (frequency === 2) {

                splitArray(betweenMonths, 2).forEach((months) => {
                    if (months.length === 2) {
                        if (endDate__.month === DateTime.fromFormat(months[1], 'LLL-yyyy').month && endDate__.year === DateTime.fromFormat(months[1], 'LLL-yyyy').year) {
                            let check = checkSubmission(item.srf_, item.site, months[0] + ' to ' + months[1], old)
                            if (check.result) {

                                future_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[1], duedate: DateTime.fromFormat(months[1], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[1], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })
                            } else {
                                if (check.data) {
                                    future_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[1], duedate: DateTime.fromFormat(months[1], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[1], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })


                                }

                            }


                        } else {
                            if (Math.round(endDate__.diff(DateTime.fromFormat(months[1], 'LLL-yyyy'), 'months').months) === 1) {


                                let check = checkSubmission(item.srf_, item.site, months[0] + ' to ' + months[1], old)
                                if (check.result) {

                                    past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[1], duedate: DateTime.fromFormat(months[1], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[1], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })

                                } else {
                                    if (check.data) {
                                        past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[1], duedate: DateTime.fromFormat(months[1], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[1], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })


                                    }

                                }


                            } else {
                                let check = checkSubmission(item.srf_, item.site, months[0] + ' to ' + months[1], old)

                                if (check.result) {
                                    past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[1], duedate: DateTime.fromFormat(months[1], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[1], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })

                                } else {
                                    if (check.data) {
                                        past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[1], duedate: DateTime.fromFormat(months[1], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[1], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                                    }

                                }
                            }
                        }
                    }
                })
            } else if (frequency === 3) {

                splitArray(betweenMonths, 3).forEach((months) => {
                    if (months.length === 3) {

                        if (endDate__.month === DateTime.fromFormat(months[2], 'LLL-yyyy').month && endDate__.year === DateTime.fromFormat(months[2], 'LLL-yyyy').year) {
                            let check = checkSubmission(item.srf_, item.site, months[0] + ' to ' + months[2], old)

                            if (check.result) {

                                future_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[2], duedate: DateTime.fromFormat(months[2], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[2], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })
                            } else {
                                if (check.data) {
                                    future_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[2], duedate: DateTime.fromFormat(months[2], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[2], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                                }

                            }


                        } else {
                            if (Math.round(endDate__.diff(DateTime.fromFormat(months[2], 'LLL-yyyy'), 'months').months) === 1) {


                                let check = checkSubmission(item.srf_, item.site, months[0] + ' to ' + months[2], old)

                                if (check.result) {
                                    past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[2], duedate: DateTime.fromFormat(months[2], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[2], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })
                                } else {
                                    if (check.data) {
                                        past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[2], duedate: DateTime.fromFormat(months[2], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[2], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                                    }

                                }



                            } else {

                                let check = checkSubmission(item.srf_, item.site, months[0] + ' to ' + months[2], old)

                                if (check.result) {
                                    past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[2], duedate: DateTime.fromFormat(months[2], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[2], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })
                                } else {
                                    if (check.data) {
                                        console.log(item.site, item.srf_, months[0] + ' to ' + months[2])
                                        past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[2], duedate: DateTime.fromFormat(months[2], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[2], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                                    }

                                }
                            }
                        }
                    }
                })
            } else if (frequency === 4) {
                splitArray(betweenMonths, 12).forEach((months) => {
                    if (months.length === 12) {
                        if (endDate__.month === DateTime.fromFormat(months[11], 'LLL-yyyy').month && endDate__.year === DateTime.fromFormat(months[11], 'LLL-yyyy').year) {
                            let check = checkSubmission(item.srf_, item.site, months[0] + ' to ' + months[11], old)

                            if (check.result) {

                                future_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[11], duedate: DateTime.fromFormat(months[11], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[11], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })

                            } else {
                                if (check.data) {
                                    future_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[11], duedate: DateTime.fromFormat(months[11], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[11], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                                }

                            }


                        } else {
                            if (Math.round(endDate__.diff(DateTime.fromFormat(months[11], 'LLL-yyyy'), 'months').months) === 1) {



                                let check = checkSubmission(item.srf_, item.site, months[0] + ' to ' + months[11], old)

                                if (check.result) {

                                    past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[11], duedate: DateTime.fromFormat(months[11], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[11], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })

                                } else {
                                    if (check.data) {
                                        past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[11], duedate: DateTime.fromFormat(months[11], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[11], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                                    }

                                }


                            } else {
                                let check = checkSubmission(item.srf_, item.site, months[0] + ' to ' + months[11], old)

                                if (check.result) {

                                    past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[11], duedate: DateTime.fromFormat(months[11], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[11], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })

                                } else {
                                    if (check.data) {
                                        past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[11], duedate: DateTime.fromFormat(months[11], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[11], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                                    }

                                }
                            }
                        }
                    }
                })
            } else if (frequency === 5) {
                splitArray(betweenMonths, 6).forEach((months) => {
                    if (months.length === 6) {
                        if (endDate__.month === DateTime.fromFormat(months[5], 'LLL-yyyy').month && endDate__.year === DateTime.fromFormat(months[5], 'LLL-yyyy').year) {
                            let check = checkSubmission(item.srf_, item.site, months[0] + ' to ' + months[5], old)

                            if (check.result) {


                                future_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[5], duedate: DateTime.fromFormat(months[5], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[5], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })
                            } else {
                                if (check.data) {
                                    future_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[5], duedate: DateTime.fromFormat(months[5], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[5], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                                }

                            }


                        } else {
                            if (Math.round(endDate__.diff(DateTime.fromFormat(months[5], 'LLL-yyyy'), 'months').months) === 1) {


                                let check = checkSubmission(item.srf_, item.site, months[0] + ' to ' + months[5], old)

                                if (check.result) {


                                    past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[5], duedate: DateTime.fromFormat(months[5], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[5], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })
                                } else {
                                    if (check.data) {
                                        past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[5], duedate: DateTime.fromFormat(months[5], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[5], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                                    }

                                }



                            } else {
                                let check = checkSubmission(item.srf_, item.site, months[0] + ' to ' + months[5], old)

                                if (check.result) {


                                    past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[5], duedate: DateTime.fromFormat(months[5], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[5], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: check.data.response, id: check.data.id, reject: check.data.reject, draft: check.data })
                                } else {
                                    if (check.data) {
                                        past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0] + ' to ' + months[5], duedate: DateTime.fromFormat(months[5], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[5], 'LLL-yyyy').plus({ months: 1 }), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                                    }

                                }
                            }
                        }
                    }
                })
            } else if (frequency === 6) {
                splitArray(betweenMonths, 1).forEach((months, ind) => {

                    if (endDate__.month === DateTime.fromFormat(months[0], 'LLL-yyyy').month && endDate__.year === DateTime.fromFormat(months[0], 'LLL-yyyy').year) {
                        let check = checkSubmission(item.srf_, item.site, months[0], old)

                        if (check.result) {
                            past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, dcftype: item.srf_.type, rp: months[0], duedate: DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[0], 'LLL-yyyy'), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                            check.list.forEach((list) => {
                                past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, dcftype: item.srf_.type, rp: months[0], duedate: DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[0], 'LLL-yyyy'), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: list.response, id: list.id, reject: list.reject, draft: list })

                            })

                        } else {
                            if (check.data) {
                                past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, dcftype: item.srf_.type, rp: months[0], duedate: DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[0], 'LLL-yyyy'), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                            }

                        }


                    } else {
                        let check = checkSubmission(item.srf_, item.site, months[0], old)

                        if (check.result) {

                            check.list.forEach((list) => {
                                past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0], duedate: DateTime.fromFormat(months[0], 'LLL-yyyy').plus({ months: 1 }).toFormat('LLL-yyyy'), overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[0], 'LLL-yyyy'), 'days').days), site: item.site, company_id: login_data.information.cid, frequency, response: list.response, id: list.id })

                            })


                        } else {
                            if (check.data) {
                                past_.push({ assignmentId: item.id, self: item.reviewer_id === item.user_id, standard: item.standard === null ? 0 : item.standard, srf: item.srf_, dcftype: item.srf_.type, rp: months[0], duedate: months[0], overdue: Math.round(endDate__.diff(DateTime.fromFormat(months[0], 'LLL-yyyy'), 'days').days), site: item.site, company_id: login_data.information.cid, frequency })

                            }

                        }


                    }

                })
            }

        }
        console.log(future_)

        setPast((prev) => ([...prev, ...past_]))
        setPastBK((prev) => ([...prev, ...past_]))
        setPresent((prev) => ([...prev, ...present_]))
        setFuture((prev) => ([...prev, ...future_]))



        return { type, months: betweenMonths }
    }
    const checkSubmission = (dcf, site, rp, old) => {
        // let rps = getRP(rp)
        let rps = getRPLuxon(rp)

        let result = {}
        let loc = JSON.parse(JSON.stringify(old))

        let index = loc.findIndex((i) => {
            console.log(compareArrays(i.reporting_period, rps), i.site, site, i.cf === dcf.id, i.type === 0)
            return compareArrays(i.reporting_period, rps) && i.site === site && i.cf === dcf.id && i.type === 0
        })
        let index2 = loc.findIndex((i) => {

            return compareArrays(i.reporting_period, rps) && i.site === site && i.cf === dcf.id && (i.type === 1 || i.type === 2 || i.type === 3)
        })
        console.log(index)
        if (index === -1) {

            result = { result: false, data: index2 === -1 ? true : false }
        } else {
            result = {
                result: true, data: loc[index], list: loc.filter((i) => {
                    return compareArrays(i.reporting_period, rps) && i.site === site && i.cf === dcf.id && i.type === 0
                })
            }
        }
        console.log(old, rp, rps, dcf, site, result)
        return result
    }
    const compareArrays = (a, b) => {


        return JSON.stringify(a) === JSON.stringify(b);
    };
    const splitArray = (array, chunkSize) => {


        return array.reduce((resultArray, item, index) => {
            const chunkIndex = Math.floor(index / chunkSize)

            if (!resultArray[chunkIndex]) {
                resultArray[chunkIndex] = [] // start a new chunk
            }

            resultArray[chunkIndex].push(item)

            return resultArray
        }, [])
    }

    const getRPLuxon = (months) => {
        if (months.includes('to')) {
            let startDate = DateTime.fromFormat(months.split('to')[0].trim(), 'LLL-yyyy')
            let endDate = DateTime.fromFormat(months.split('to')[1].trim(), 'LLL-yyyy')
            let rp = []
            while (startDate <= endDate) {
                rp.push(startDate.toFormat('LL-yyyy'));
                startDate = startDate.plus({ months: 1 })
            }
            return rp
        } else {
            return [DateTime.fromFormat(months, 'LLL-yyyy').toFormat('LL-yyyy')]
        }
    }
    const onSiteSelected = (val) => {

        setPast((prev) => [])
        setPastBK((prev) => [])
        setPresent((prev) => ([]))
        setFuture((prev) => ([]))
        forceUpdate()
        setSelectedSite(val)
    }

    const freezeFilter = () => {
        let stickyElem = document.querySelector(".sticky-div");
        if (stickyElem && document.getElementById("divsize") !== null) {
            let currStickyPos = stickyElem.getBoundingClientRect().top + window.pageYOffset;


            if (window.pageYOffset > currStickyPos) {
                stickyElem.style.position = "fixed";
                stickyElem.style.top = "98px";
                stickyElem.style.background = "#F9F9F9";
                stickyElem.style.width =
                    document.getElementById("divsize").clientWidth + "px";

                stickyElem.style.zIndex = 999;
            } else {
                stickyElem.style.position = "relative";
                stickyElem.style.top = "initial";
                stickyElem.style.width = "100%";
                stickyElem.style.background = "transparent";
                stickyElem.style.zIndex = 1;
            }

        }
    };


    const getReviewer = (srfId, siteid, val) => {
        let approverIndex = ass.findIndex((i) => { return i.site === siteid && i.srfId === srfId && i.user_id === login_data.id })
        console.log(approverIndex, srfId, siteid, ass)
        if (approverIndex !== -1) {
            let approverIndex2 = supplierList.findIndex((i) => { return i.id === ass[approverIndex].approver_id })
            if (approverIndex2 !== -1) {

                return supplierList[approverIndex2].information.empname.trim().toLowerCase().includes(val.trim().toLowerCase())
            } else { return false }
        } else {
            return false
        }
    }
    const getStatus = (val, rowData) => {
        let text = 'NOT SUBMITTED'
        console.log(rowData)
        if (rowData.draft !== undefined) {
            if (rowData.draft.type === 0) {
                if (rowData.draft.reject === 1) {

                    text = 'RETURNED'

                } else {

                    text = 'DRAFT'

                }
            } else if (rowData.draft.type === 1) {

                text = 'SUBMITTED'

            } else if (rowData.draft.type === 2) {

                if (rowData.draft.self) {
                    text = 'Self Reviewed'
                } else {
                    text = 'Reviewed'
                }


            } else if (rowData.draft.type === 3) {

                text = 'APPROVED'

            }
        }
        return text.trim().toLowerCase().includes(val)
    }
    const getSite = (id_, val) => {
        let index = sitelist.findIndex((i) => { return i.id === id_ })
        if (index !== -1) {
            console.log(sitelist[index].name.trim().toLowerCase().includes(val.trim().toLowerCase()))
            return sitelist[index].name.trim().toLowerCase().includes(val.trim().toLowerCase())
        } else {
            return false
        }

    }
    const getStatusText = (val, rowData) => {
        let text = 'NOT SUBMITTED'
        console.log(rowData)
        if (rowData.draft !== undefined) {
            if (rowData.draft.type === 0) {
                if (rowData.draft.reject === 1) {

                    text = 'RETURNED'

                } else {

                    text = 'DRAFT'

                }
            } else if (rowData.draft.type === 1) {

                text = 'SUBMITTED'

            } else if (rowData.draft.type === 2) {

                if (rowData.draft.self) {
                    text = 'Self Reviewed'
                } else {
                    text = 'Reviewed'
                }


            } else if (rowData.draft.type === 3) {

                text = 'APPROVED'

            }
        }
        return text
    }
    const sortRP = (e) => {

        if (e.order === 1) {
            return e.data.sort((a, b) => {

                let dateA = DateTime.fromFormat(a.rp, 'MMM-yyyy');
                let dateB = DateTime.fromFormat(b.rp, 'MMM-yyyy');
                if (a.rp.includes('to')) {

                    dateA = DateTime.fromFormat(a.rp.split('to')[0].trim(), 'MMM-yyyy');

                }
                if (b.rp.includes('to')) {
                    dateB = DateTime.fromFormat(b.rp.split('to')[0].trim(), 'MMM-yyyy');
                }

                // Compare the parsed dates
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            })

        } else {
            return e.data.sort((a, b) => {
                let dateA = DateTime.fromFormat(a.rp, 'MMM-yyyy');
                let dateB = DateTime.fromFormat(b.rp, 'MMM-yyyy');
                if (a.rp.includes('to')) {

                    dateA = DateTime.fromFormat(a.rp.split('to')[0].trim(), 'MMM-yyyy');

                }
                if (b.rp.includes('to')) {
                    dateB = DateTime.fromFormat(b.rp.split('to')[0].trim(), 'MMM-yyyy');
                }
                // Compare the parsed dates in descending order
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            })
        }


    }
    const sortOD = (e) => {
        if (e.order === -1) {
            return e.data.sort((a, b) => {
                const dateA = DateTime.fromFormat(a.duedate, 'MMM-yyyy');
                const dateB = DateTime.fromFormat(b.duedate, 'MMM-yyyy');

                // Compare the parsed dates
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            })

        } else {
            return e.data.sort((a, b) => {
                const dateA = DateTime.fromFormat(a.duedate, 'MMM-yyyy');
                const dateB = DateTime.fromFormat(b.duedate, 'MMM-yyyy');

                // Compare the parsed dates in descending order
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            })
        }


    }
    const sortStatus = (e) => {

        if (e.order === 1) {
            return e.data.sort((a, b) => {
                const dateA = getStatusText('', a)
                const dateB = getStatusText('', b)
                console.log(dateA, dateB)
                // Compare the parsed dates
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            })

        } else {
            return e.data.sort((a, b) => {
                const dateA = getStatusText('', a)
                const dateB = getStatusText('', b)

                // Compare the parsed dates in descending order
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            })
        }
    }

    return (
        <div className="bg-smoke font-lato" >
            <div className="col-12" >
                <div style={{}} >
                    <div className="col-12 p-5" style={{
                        justifyContent: 'flex-start'
                    }}>
                        <label className="text-big-one clr-navy flex fs-16 flex justify-content-start"> DATA SUBMISSION AND REVIEW PORTAL
                        </label>
                        <label className="text-micro clr-navy flex  justify-content-start">Your one-stop portal to view and take action on all your sustainability data submission and approvals.</label>

                    </div>
                    {/* <TabMenu model={items} activeIndex={activetab} onTabChange={(e) => setActiveTab(e.index)} /> */}
                    {(show.approver || show.entry) ? <div classname='bg-white'>
                        {/* <div className="col-12">
                        <div className="grid" style={{ justifyContent: 'space-between' }}>
                            {show.entry && <>    <div className=" p-card" style={{ width: !show.approver ? '22%' : '14%', position: 'relative', background: activetab === 1 ? 'darkgrey' : 'white' }} onClick={() => { activetab !== 1 && setActiveTab(1) }}>
                                <label style={{ display: 'flex', padding: 25, justifyContent: 'center', fontWeight: 600, color: activetab === 1 && 'white' }}>Reporter Data Input</label>
                                <label style={{
                                    position: 'absolute',
                                    bottom: '10px',
                                    color: activetab === 1 ? 'white' : 'red',
                                    right: '10px',
                                    fontSize: '20px'
                                }}>{past.length + present.length}</label>
                            </div>
                                <div className=" p-card" style={{ width: !show.approver ? '22%' : '14%', position: 'relative', background: activetab === 2 ? 'darkgrey' : 'white' }} onClick={() => { activetab !== 2 && setActiveTab(2) }}>
                                    <label style={{ display: 'flex', padding: 25, justifyContent: 'center', fontWeight: 600, color: activetab === 2 && 'white' }}>Submitted Data Logs</label>
                                    <label style={{
                                        position: 'absolute',
                                        bottom: '10px',
                                        color: activetab === 2 && 'white',
                                        right: '10px',
                                        fontSize: '20px'
                                    }}>{submitteddcf.filter((i) => { return (i.type !== 0 || i.reject === 1) && i.submitted_by === login_data.id }).length}</label>
                                </div>
                           
                            </>}
                            {show.approver && <> <div className=" p-card" style={{ width: show.approver ? '18.5%' : '32%', position: 'relative', background: activetab === 3 ? 'darkgrey' : 'white' }} onClick={() => { activetab !== 3 && setActiveTab(3) }}>
                                <label style={{ display: 'flex', padding: 25, justifyContent: 'center', fontWeight: 600, color: activetab === 3 && 'white' }}>Submission Reviewer Portal</label>
                                <label style={{
                                    position: 'absolute',
                                    bottom: '10px',
                                    color: activetab === 3 ? 'white' : 'red',
                                    right: '10px',
                                    fontSize: '20px'
                                }}>{pendingap.filter((i) => { return (i.type === 1 || i.type === 2) }).length}</label>
                            </div>
                                <div className=" p-card" style={{ width: show.approver ? '18.5%' : '32%', position: 'relative', background: activetab === 4 ? 'darkgrey' : 'white' }} onClick={() => { activetab !== 4 && setActiveTab(4) }}>
                                    <label style={{ display: 'flex', padding: 25, justifyContent: 'center', fontWeight: 600, color: activetab === 4 && 'white' }}>Approved Data Logs</label>
                                    <label style={{
                                        position: 'absolute',
                                        bottom: '10px',
                                        color: activetab === 4 && 'white',
                                        right: '10px',
                                        fontSize: '20px'
                                    }}>{pendingap.filter((i) => { return (i.reject === 1 || i.type === 3) }).length}</label>
                                </div>
                            </>}
                            {login_data.id !== login_data.clientId && <div className=" p-card" style={{ width: (show.entry && show.approver) ? '14%' : show.entry ? '22%' : '32%', position: 'relative', background: activetab === 5 ? 'darkgrey' : 'white' }} onClick={() => { activetab !== 5 && setActiveTab(5) }}>
                                <label style={{ display: 'flex', padding: 25, justifyContent: 'center', fontWeight: 600, color: activetab === 5 && 'white' }}>Qualitative Response</label>
                                <label style={{
                                    position: 'absolute',
                                    bottom: '10px',
                                    color: activetab === 5 ? 'white' : 'red',
                                    right: '10px',
                                    fontSize: '20px'
                                }}>{rfass.filter((k) => { return k.user_id === login_data.id }).length}</label>
                            </div>}
                        </div>
                    </div> */}
                        <div className="col-12">
                            <div className="col-8 p-4 grid align-items-center" style={{ marginBottom: 5 }} >
                                <div className="col-2">
                                    <label>Select Site : &nbsp; &nbsp;</label>
                                </div>
                                <div className="col-8">

                                    <Dropdown options={sitelist} optionValue='id' style={{ width: '100%' }} optionLabel='name' value={selectedsite} onChange={(e) => { onSiteSelected(e.value) }} />

                                </div>
                            </div>

                        </div>
                        <div className="col-12  justify-content-between">
                            <div className="col-12">
                                {show.entry &&
                                    <div className="p-card">
                                        <label className="text-big-one clr-gray-900 flex fs-16 fw-7 p-2  flex"> Your Submissions </label>
                                        <div className="col-12 flex justify-content-evenly" style={{ borderTop: '1px solid #E0E0E0' }}>
                                            <div className="col-12 grid">
                                                <div className="col-4 flex justify-content-center" >
                                                    <div className=" flex justify-content-center admin-card" style={{ height: 120 }} onMouseLeave={(e) => { e.currentTarget.style.background = '#ffffff'; e.currentTarget.className = ' flex admin-card justify-content-center '; e.target.style.cursor = 'pointer' }} onMouseOver={(e) => { e.preventDefault(); e.currentTarget.style.backgroundColor = '#31597510'; e.currentTarget.className = ' flex justify-content-center admin-card  box-shadow-one' }} onClick={() => { navigate.push({ pathname: '/srf_reporter_overdue/' + selectedsite }) }} >
                                                        <div className="flex justify-content-between align-items-center " style={{ flexDirection: 'row' }}>
                                                            <div className="big-number-navy col-2 fs-36">{past.length}</div>
                                                            <div className='col-10  grid justify-content-between'>
                                                                <div className="clr-navy   fs-18 text-bold">Quantitative</div>
                                                                <div className="flex align-items-center"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M4 12H20M20 12L14 6M20 12L14 18" stroke="#315975" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                                </svg>
                                                                </div>
                                                            </div>

                                                        </div>
                                                        {/* <div className="justify-content-between grid">
                                                            <div className="clr-navy fs-12 text-bold">REQUIRED SUBMISSIONS</div>
                                                            <div><svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M4 12H20M20 12L14 6M20 12L14 18" stroke="#315975" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                            </svg>
                                                            </div>
                                                        </div> */}
                                                    </div>
                                                </div>
                                                <div className="col-4 flex justify-content-center" >
                                                    <div className=" flex justify-content-center admin-card" style={{ height: 120 }} onMouseLeave={(e) => { e.currentTarget.style.background = '#ffffff'; e.currentTarget.className = ' flex admin-card justify-content-center '; e.target.style.cursor = 'pointer' }} onMouseOver={(e) => { e.preventDefault(); e.currentTarget.style.backgroundColor = '#31597510'; e.currentTarget.className = ' flex justify-content-center admin-card box-shadow-one' }} onClick={() => {  }} >
                                                        <div className="flex justify-content-between align-items-center " style={{ flexDirection: 'row' }}>
                                                            <div className="big-number-navy col-2 fs-36">{0}</div>
                                                            <div className='col-10  grid justify-content-between'>
                                                                <div className="clr-navy   fs-18 text-bold">Qualitative</div>
                                                                <div className="flex align-items-center"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M4 12H20M20 12L14 6M20 12L14 18" stroke="#315975" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                                </svg>
                                                                </div>
                                                            </div>
                                                        </div>                                                      
                                                    </div>
                                                </div>
                                                <div className="col-4 flex justify-content-center" >
                                                    <div className=" flex justify-content-center admin-card" style={{ height: 120 }} onMouseLeave={(e) => { e.currentTarget.style.background = '#ffffff'; e.currentTarget.className = ' flex admin-card justify-content-center '; e.target.style.cursor = 'pointer' }} onMouseOver={(e) => { e.preventDefault(); e.currentTarget.style.backgroundColor = '#31597510'; e.currentTarget.className = ' flex justify-content-center admin-card  box-shadow-one' }} onClick={() => { navigate.push({ pathname: '/srf_reporter_historic/' + selectedsite }) }} >
                                                        <div className="flex justify-content-between align-items-center " style={{ flexDirection: 'row' }}>
                                                            <div className="big-number-navy col-2 fs-36">{submitteddcf.filter((i) => { return i.form_type === 2 && i.type !== 0 && (i.site === selectedsite || selectedsite === 0) && i.submitted_by === login_data.id }).length}</div>
                                                            <div className='col-10  grid justify-content-between'>
                                                                <div className="clr-navy   fs-18 text-bold">Historic</div>
                                                                <div className="flex align-items-center"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M4 12H20M20 12L14 6M20 12L14 18" stroke="#315975" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                                </svg>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {/* <div className="justify-content-between grid">
                                                            <div className="clr-navy fs-12 text-bold">HISTORIC</div>
                                                            <div><svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M4 12H20M20 12L14 6M20 12L14 18" stroke="#315975" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                            </svg>
                                                            </div>
                                                        </div> */}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                }
                                {show.approver &&
                                    <div className="p-card mt-4">
                                        <label className="text-big-one clr-gray-900 fw-7 p-2 flex fs-16 flex"> Your Reviews </label>
                                        <div className="col-12 flex justify-content-evenly" style={{ borderTop: '1px solid #E0E0E0' }}>
                                            <div className="col-12 grid">
                                                <div className="col-4 flex justify-content-center" >
                                                    <div className=" flex justify-content-center admin-card" style={{ height: 120 }} onMouseLeave={(e) => { e.currentTarget.style.background = '#ffffff'; e.currentTarget.className = ' flex admin-card justify-content-center '; e.target.style.cursor = 'pointer' }} onMouseOver={(e) => { e.preventDefault(); e.currentTarget.style.backgroundColor = '#31597510'; e.currentTarget.className = ' flex justify-content-center admin-card  box-shadow-one' }} onClick={() => { navigate.push({ pathname: '/srf_reviewer_overdue/' + selectedsite, past }) }} >
                                                        <div className="flex justify-content-between align-items-center " style={{ flexDirection: 'row' }}>
                                                            <div className="big-number-navy col-2 fs-36">{pendingap.filter((i) => { return (i.type === 1) }).filter(obj => {
                                                                const lastReportingPeriod = obj.reporting_period[obj.reporting_period.length - 1];
                                                                const nextMonth = DateTime.fromFormat(lastReportingPeriod, 'MM-yyyy').plus({ months: 1 }).toLocal();
                                                                console.log((DateTime.local().diff(nextMonth, 'months').months).toFixed(0), nextMonth.month, DateTime.local().month);
                                                                return (DateTime.local().diff(nextMonth, 'months').months).toFixed(0) > 0
                                                            }).length}</div>
                                                            <div className='col-10  grid justify-content-between'>
                                                                <div className="clr-navy   fs-18 text-bold">Quantitative</div>
                                                                <div className="flex align-items-center"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M4 12H20M20 12L14 6M20 12L14 18" stroke="#315975" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                                </svg>
                                                                </div>
                                                            </div>

                                                        </div>
                                                        {/* <div className="justify-content-between grid">
                                                            <div className="clr-navy fs-12 text-bold">REQUIRED SUBMISSIONS</div>
                                                            <div><svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M4 12H20M20 12L14 6M20 12L14 18" stroke="#315975" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                            </svg>
                                                            </div>
                                                        </div> */}
                                                    </div>
                                                </div>
                                                <div className="col-4 flex justify-content-center" >
                                                    <div className=" flex justify-content-center admin-card" style={{ height: 120 }} onMouseLeave={(e) => { e.currentTarget.style.background = '#ffffff'; e.currentTarget.className = ' flex admin-card justify-content-center '; e.target.style.cursor = 'pointer' }} onMouseOver={(e) => { e.preventDefault(); e.currentTarget.style.backgroundColor = '#31597510'; e.currentTarget.className = ' flex justify-content-center admin-card box-shadow-one' }} onClick={() => {  }} >
                                                        <div className="flex justify-content-between align-items-center " style={{ flexDirection: 'row' }}>
                                                            <div className="big-number-navy col-2 fs-36">{0}</div>
                                                            <div className='col-10  grid justify-content-between'>
                                                                <div className="clr-navy   fs-18 text-bold">Qualitative</div>
                                                                <div className="flex align-items-center"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M4 12H20M20 12L14 6M20 12L14 18" stroke="#315975" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                                </svg>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    
                                                    </div>
                                                </div>
                                                <div className="col-4 flex justify-content-center" >
                                                    <div className=" flex justify-content-center admin-card" style={{ height: 120 }} onMouseLeave={(e) => { e.currentTarget.style.background = '#ffffff'; e.currentTarget.className = ' flex admin-card justify-content-center '; e.target.style.cursor = 'pointer' }} onMouseOver={(e) => { e.preventDefault(); e.currentTarget.style.backgroundColor = '#31597510'; e.currentTarget.className = ' flex justify-content-center admin-card  box-shadow-one' }} onClick={() => { navigate.push({ pathname: '/srf_reviewer_historic/' + selectedsite, past }) }} >
                                                        <div className="flex justify-content-between align-items-center " style={{ flexDirection: 'row' }}>
                                                            <div className="big-number-navy col-2 fs-36">{submitteddcf.filter((i) => { return i.form_type === 2 && i.type === 2 && (i.site === selectedsite || selectedsite === 0) && i.reviewed_by === login_data.id }).length}</div>
                                                            <div className='col-10  grid justify-content-between'>
                                                                <div className="clr-navy   fs-18 text-bold">Historic</div>
                                                                <div className="flex align-items-center"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M4 12H20M20 12L14 6M20 12L14 18" stroke="#315975" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                                </svg>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                       
                                     
                                    </div>
                                }



                            </div>

                            <div className="col-12">

                                <div style={{ background: '#41AA950D' }}>
                                    <label className="fw-7 fs-16 clr-gray-900 flex p-3">Recent updates</label>
                                    <div style={{ overflow: 'auto', height: 450 }}>
                                        <label className="m-2 flex justify-content-center" >no updates</label>
                                        {[].map((i) => {
                                            return (
                                                <div style={{ borderBottom: '1px solid #E0E0E0' }}>
                                                    <div className="p-1 flex m-4" style={{ flexDirection: 'column' }} >
                                                        <label className="fs-16 fw-4 clr-gray-900" style={{ width: '100%' }}>{i.title}</label>
                                                        <label classname='fw-4 fs-16 ' style={{ width: '100%', color: '#828282', marginTop: 20 }}>{i.date}
                                                            <svg width="6" height="6" viewBox="0 0 6 6" fill="none" style={{ marginLeft: 5, marginRight: 5 }} xmlns="http://www.w3.org/2000/svg">
                                                                <circle cx="3" cy="3" r="3" fill="#828282" />
                                                            </svg> {i.time}
                                                        </label>

                                                    </div>
                                                </div>
                                            )
                                        })}
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div> :
                        <label style={{
                            justifyContent: 'center',
                            display: 'flex',
                            marginTop: '50px'
                        }}>
                            {!show.loaded ? <i className="pi pi-spin pi-spinner" style={{ fontSize: 50 }}></i> :
                                'Nothing configured to your login'
                            }
                        </label>
                    }

                </div>
            </div>
        </div>
    );
};

const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(SupplierHome, comparisonFn);

import React, { useEffect, useRef } from "react";
import * as d3 from "d3";

const data = [
  { week: "Week 1", day: "Monday", value: 30 },
  { week: "Week 1", day: "Tuesday", value: 40 },
  { week: "Week 1", day: "Wednesday", value: 50 },
  { week: "Week 1", day: "Thursday", value: 35 },
  { week: "Week 1", day: "Friday", value: 25 },
  { week: "Week 2", day: "Monday", value: 20 },
  { week: "Week 2", day: "Tuesday", value: 45 },
  { week: "Week 2", day: "Wednesday", value: 60 },
  { week: "Week 2", day: "Thursday", value: 55 },
  { week: "Week 2", day: "Friday", value: 30 },
  { week: "Week 3", day: "Monday", value: 15 },
  { week: "Week 3", day: "Tuesday", value: 50 },
  { week: "Week 3", day: "Wednesday", value: 70 },
  { week: "Week 3", day: "Thursday", value: 65 },
  { week: "Week 3", day: "Friday", value: 40 },
];

const TrainingHeatMap = () => {
  const chartRef = useRef(null);

  useEffect(() => {
    renderChart();
  }, []);

  const renderChart = () => {
    const width = 800;
    const height = 500;
    const margin = { top: 50, right: 50, bottom: 100, left: 100 };

    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height);

    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Get unique weeks and days
    const weeks = [...new Set(data.map((d) => d.week))];
    const days = [...new Set(data.map((d) => d.day))];

    // Scales
    const x = d3.scaleBand().domain(weeks).range([0, chartWidth]).padding(0.1);

    const y = d3.scaleBand().domain(days).range([0, chartHeight]).padding(0.1);

    const color = d3
      .scaleSequential(d3.interpolateBlues)
      .domain([0, d3.max(data, (d) => d.value)]);

    // X-axis
    g.append("g")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(x));

    // Y-axis
    g.append("g").call(d3.axisLeft(y));

    // X-axis label
    g.append("text")
      .attr("x", chartWidth / 2)
      .attr("y", chartHeight + 40)
      .style("text-anchor", "middle")
      .style("font-size", "14px")
      .text("Date (Week)");

    // Y-axis label
    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("x", -chartHeight / 2)
      .attr("y", -60)
      .style("text-anchor", "middle")
      .style("font-size", "14px")
      .text("Day of the Week");

    // Draw heatmap cells
    g.selectAll("rect")
      .data(data)
      .enter()
      .append("rect")
      .attr("x", (d) => x(d.week))
      .attr("y", (d) => y(d.day))
      .attr("width", x.bandwidth())
      .attr("height", y.bandwidth())
      .attr("fill", (d) => color(d.value))
      .append("title")
      .text((d) => `${d.week}, ${d.day}: ${d.value}`);

    // Add legend
    const legendWidth = 300;
    const legendHeight = 20;

    const legend = svg
      .append("g")
      .attr(
        "transform",
        `translate(${chartWidth / 2 - legendWidth / 2},${
          height - margin.bottom + 30
        })`
      );

    const legendScale = d3
      .scaleLinear()
      .domain(color.domain())
      .range([0, legendWidth]);

    const legendAxis = d3
      .axisBottom(legendScale)
      .ticks(5)
      .tickSize(-legendHeight);

    legend
      .selectAll("rect")
      .data(
        d3.range(color.domain()[0], color.domain()[1], color.domain()[1] / 20)
      )
      .enter()
      .append("rect")
      .attr("x", (d) => legendScale(d))
      .attr("y", 0)
      .attr("width", legendWidth / 20)
      .attr("height", legendHeight)
      .attr("fill", (d) => color(d));

    legend
      .append("g")
      .attr("transform", `translate(0,${legendHeight})`)
      .call(legendAxis);
  };

  return (
    <>
      <h3 style={{ fontSize: "18px" }}>Training Intensity Heat Map</h3>

      <div
        ref={chartRef}
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          marginTop: "20px",
        }}
      />
    </>
  );
};

export default TrainingHeatMap;

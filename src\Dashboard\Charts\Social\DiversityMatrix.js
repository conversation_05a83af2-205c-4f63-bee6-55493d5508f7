import React, { useEffect, useRef } from "react";
import * as d3 from "d3";

const DiversityMatrix = () => {
  const chartRef = useRef(null);

  const data = [
    { gender: "Male", ageGroup: "18-25", count: 30 },
    { gender: "Male", ageGroup: "26-35", count: 40 },
    { gender: "Male", ageGroup: "36-45", count: 50 },
    { gender: "Male", ageGroup: "46-60", count: 60 },
    { gender: "Female", ageGroup: "18-25", count: 25 },
    { gender: "Female", ageGroup: "26-35", count: 35 },
    { gender: "Female", ageGroup: "36-45", count: 45 },
    { gender: "Female", ageGroup: "46-60", count: 55 },
    { gender: "Non-Binary", ageGroup: "18-25", count: 10 },
    { gender: "Non-Binary", ageGroup: "26-35", count: 15 },
    { gender: "Non-Binary", ageGroup: "36-45", count: 20 },
    { gender: "Non-Binary", ageGroup: "46-60", count: 25 },
  ];

  useEffect(() => {
    renderChart();
  }, []);

  const renderChart = () => {
    const margin = { top: 40, right: 20, bottom: 40, left: 40 };
    const width = 540;
    const height = 400;

    // Remove any existing SVG for re-rendering
    d3.select(chartRef.current).select("svg").remove();

    // Create the SVG canvas
    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const ageGroups = ["18-25", "26-35", "36-45", "46-60"];
    const genders = ["Male", "Female", "Non-Binary"];

    // Color scale
    const colorScale = d3
      .scaleSequential(d3.interpolateBlues)
      .domain([0, d3.max(data, (d) => d.count)]);

    // Create the X and Y scales
    const xScale = d3
      .scaleBand()
      .domain(ageGroups)
      .range([0, width])
      .padding(0.05);

    const yScale = d3
      .scaleBand()
      .domain(genders)
      .range([0, height])
      .padding(0.05);

    // Append the axis
    svg
      .append("g")
      .attr("class", "x-axis")
      .attr("transform", `translate(0, ${height})`)
      .call(d3.axisBottom(xScale));

    svg.append("g").attr("class", "y-axis").call(d3.axisLeft(yScale));

    // Create the cells in the matrix
    svg
      .selectAll("rect")
      .data(data)
      .enter()
      .append("rect")
      .attr("x", (d) => xScale(d.ageGroup))
      .attr("y", (d) => yScale(d.gender))
      .attr("width", xScale.bandwidth())
      .attr("height", yScale.bandwidth())
      .attr("fill", (d) => colorScale(d.count))
      .style("stroke", "#fff")
      .style("stroke-width", 1);

    // Add text labels for the count in each cell
    svg
      .selectAll("text")
      .data(data)
      .enter()
      .append("text")
      .attr("x", (d) => xScale(d.ageGroup) + xScale.bandwidth() / 2)
      .attr("y", (d) => yScale(d.gender) + yScale.bandwidth() / 2)
      .attr("dy", ".35em") // Vertically center the text
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", "#fff")
      .text((d) => d.count);

    // Title for the chart
    svg
      .append("text")
      .attr("x", width / 2)
      .attr("y", -margin.top / 2)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .text("Diversity Matrix: Gender vs. Age Group");
  };

  return (
    <div
      ref={chartRef}
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    />
  );
};

export default DiversityMatrix;

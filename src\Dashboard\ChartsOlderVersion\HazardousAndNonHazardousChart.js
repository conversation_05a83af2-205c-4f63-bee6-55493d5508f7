import React, { useState, useRef } from "react";
import { But<PERSON> } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { CustomLegend } from "./Dashboard";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  LabelList,
} from "recharts";
import HazardousWasteChart from "./HazardousWasteChart";

const CustomFuelLegend = (props) => {
  const { payload } = props;
  return (
    <ul
      style={{
        display: "flex",
        listStyleType: "none",
        justifyContent: "center",
        padding: 0,
      }}
    >
      {payload.map(
        (entry, index) =>
          index <= 1 && (
            <li
              key={`item-${index}`}
              style={{
                color: entry.color,

                marginRight: "5px",
              }}
            >
              <span
                style={{
                  color: entry.color,
                  marginRight: 4,
                  fontSize: "20px",
                }}
              >
                ■
              </span>
              <span style={{ color: "#555", fontSize: "14px" }}>
                {entry.value}
              </span>
            </li>
          )
      )}
    </ul>
  );
};

export const HaNData = {
  2019: [
    { month: "Jan", hazardous: 120, nonHazardous: 85 },
    { month: "Feb", hazardous: 150, nonHazardous: 60 },
    { month: "Mar", hazardous: 95, nonHazardous: 110 },
    { month: "Apr", hazardous: 160, nonHazardous: 45 },
    { month: "May", hazardous: 140, nonHazardous: 80 },
    { month: "Jun", hazardous: 90, nonHazardous: 130 },
    { month: "Jul", hazardous: 130, nonHazardous: 70 },
    { month: "Aug", hazardous: 180, nonHazardous: 40 },
    { month: "Sep", hazardous: 85, nonHazardous: 125 },
    { month: "Oct", hazardous: 145, nonHazardous: 60 },
    { month: "Nov", hazardous: 95, nonHazardous: 110 },
    { month: "Dec", hazardous: 110, nonHazardous: 95 },
  ],
  2020: [
    { month: "Jan", hazardous: 170, nonHazardous: 50 },
    { month: "Feb", hazardous: 140, nonHazardous: 90 },
    { month: "Mar", hazardous: 160, nonHazardous: 65 },
    { month: "Apr", hazardous: 120, nonHazardous: 100 },
    { month: "May", hazardous: 155, nonHazardous: 75 },
    { month: "Jun", hazardous: 130, nonHazardous: 85 },
    { month: "Jul", hazardous: 140, nonHazardous: 95 },
    { month: "Aug", hazardous: 115, nonHazardous: 110 },
    { month: "Sep", hazardous: 130, nonHazardous: 105 },
    { month: "Oct", hazardous: 150, nonHazardous: 90 },
    { month: "Nov", hazardous: 95, nonHazardous: 140 },
    { month: "Dec", hazardous: 100, nonHazardous: 125 },
  ],
  2021: [
    { month: "Jan", hazardous: 110, nonHazardous: 120 },
    { month: "Feb", hazardous: 130, nonHazardous: 95 },
    { month: "Mar", hazardous: 125, nonHazardous: 115 },
    { month: "Apr", hazardous: 165, nonHazardous: 55 },
    { month: "May", hazardous: 135, nonHazardous: 105 },
    { month: "Jun", hazardous: 95, nonHazardous: 130 },
    { month: "Jul", hazardous: 150, nonHazardous: 70 },
    { month: "Aug", hazardous: 140, nonHazardous: 80 },
    { month: "Sep", hazardous: 155, nonHazardous: 85 },
    { month: "Oct", hazardous: 100, nonHazardous: 130 },
    { month: "Nov", hazardous: 90, nonHazardous: 160 },
    { month: "Dec", hazardous: 110, nonHazardous: 145 },
  ],
  2022: [
    { month: "Jan", hazardous: 130, nonHazardous: 95 },
    { month: "Feb", hazardous: 110, nonHazardous: 125 },
    { month: "Mar", hazardous: 160, nonHazardous: 85 },
    { month: "Apr", hazardous: 145, nonHazardous: 95 },
    { month: "May", hazardous: 125, nonHazardous: 135 },
    { month: "Jun", hazardous: 170, nonHazardous: 55 },
    { month: "Jul", hazardous: 155, nonHazardous: 75 },
    { month: "Aug", hazardous: 140, nonHazardous: 110 },
    { month: "Sep", hazardous: 130, nonHazardous: 120 },
    { month: "Oct", hazardous: 115, nonHazardous: 105 },
    { month: "Nov", hazardous: 95, nonHazardous: 140 },
    { month: "Dec", hazardous: 100, nonHazardous: 125 },
  ],
  2023: [
    { month: "Jan", hazardous: 180, nonHazardous: 55 },
    { month: "Feb", hazardous: 145, nonHazardous: 75 },
    { month: "Mar", hazardous: 135, nonHazardous: 110 },
    { month: "Apr", hazardous: 120, nonHazardous: 140 },
    { month: "May", hazardous: 155, nonHazardous: 85 },
    { month: "Jun", hazardous: 105, nonHazardous: 130 },
    { month: "Jul", hazardous: 135, nonHazardous: 105 },
    { month: "Aug", hazardous: 115, nonHazardous: 130 },
    { month: "Sep", hazardous: 130, nonHazardous: 115 },
    { month: "Oct", hazardous: 90, nonHazardous: 160 },
    { month: "Nov", hazardous: 125, nonHazardous: 120 },
    { month: "Dec", hazardous: 145, nonHazardous: 95 },
  ],
  2024: [
    { month: "Jan", hazardous: 100, nonHazardous: 140 },
    { month: "Feb", hazardous: 125, nonHazardous: 115 },
    { month: "Mar", hazardous: 165, nonHazardous: 95 },
    { month: "Apr", hazardous: 155, nonHazardous: 110 },
    { month: "May", hazardous: 95, nonHazardous: 135 },
    { month: "Jun", hazardous: 140, nonHazardous: 115 },
    { month: "Jul", hazardous: 175, nonHazardous: 80 },
    { month: "Aug", hazardous: 160, nonHazardous: 85 },
    { month: "Sep", hazardous: 120, nonHazardous: 135 },
    { month: "Oct", hazardous: 90, nonHazardous: 165 },
    { month: "Nov", hazardous: 130, nonHazardous: 120 },
    { month: "Dec", hazardous: 150, nonHazardous: 110 },
  ],
};

const HazardousAndNonHazardousChart = () => {
  const [activeMode, setActiveMode] = useState(true);
  const dt = useRef(null);
  const [year1, setYear1] = useState(2019);
  const [year2, setYear2] = useState(2020);

  // Combine data for the selected years
  const combinedData = HaNData[year1].map((item, index) => ({
    month: item.month,
    [`${year1} hazardous`]: item.hazardous,
    [`${year1} nonHazardous`]: item.nonHazardous,
    [`${year2} hazardous`]: HaNData[year2][index].hazardous,
    [`${year2} nonHazardous`]: HaNData[year2][index].nonHazardous,
  }));
  return (
    <div>
      <div
        style={{
          width: "100%",
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        <div
          style={{
            fontFamily: "Lato",
            fontSize: "16px",
            fontWeight: 700,
            lineHeight: "19.2px",
            textAlign: "left",
            margin: "18px 10px 18px 10px",
          }}
        >
          Hazardous and Non-Hazardous Waste
          <div
            style={{
              fontFamily: "Lato",
              fontSize: "12px",
              fontWeight: 300,
              lineHeight: "12.2px",
              textAlign: "left",
              margin: "18px 10px 18px 0px",
            }}
          >
            Quantity of Hazardous and Non-Hazardous waste generated
          </div>
        </div>

        <div
          style={{
            margin: "18px 10px 18px 10px",
            display: "flex",
          }}
        >
          <select
            value={year1}
            onChange={(e) => {
              setYear1(e.target.value);
            }}
            style={{
              padding: "3px",
              borderRadius: "8px",
              width: "7.5rem",
              border: "1px solid grey",
              height: "30px",
              fontFamily: "lato",
            }}
          >
            {Object.keys(HaNData)?.map((i) => {
              return <option value={i}>{i}</option>;
            })}
          </select>
          <select
            value={year2}
            onChange={(e) => {
              setYear2(e.target.value);
            }}
            style={{
              padding: "3px",
              borderRadius: "8px",
              width: "7.5rem",
              border: "1px solid grey",
              height: "30px",
              fontFamily: "lato",
            }}
          >
            {Object.keys(HaNData)?.map((i) => {
              return <option value={i}>{i}</option>;
            })}
          </select>
          <div
            className="buttons"
            style={{
              background: "#F0F2F4",
              borderRadius: "3px",
              width: "4.5rem",
              marginLeft: "10px",
              height: "30px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              style={{
                background: `${!activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
                marginRight: "4px",
              }}
              onClick={() => {
                setActiveMode(false);
              }}
            >
              <i className="pi pi-table fs-19 " />
            </Button>
            <Button
              style={{
                background: `${activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
              }}
              onClick={() => {
                setActiveMode(true);
              }}
            >
              <i className="pi pi-chart-bar fs-19" />
            </Button>
          </div>
          <Button
            style={{
              padding: "6px",
              color: "white",
              height: "30px",
              marginLeft: "2px",
            }}
            onClick={() => {
              setActiveMode(true);
            }}
          >
            <i className="pi pi-download fs-19" />
          </Button>
        </div>
      </div>
      {activeMode && (
        <ResponsiveContainer width="100.1%" height={400}>
          <BarChart
            data={combinedData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
            barGap={5}
            barSize={30}
          >
            <XAxis dataKey="month" />
            <YAxis
              label={{
                value: "Million Tons",
                angle: -90,
                position: "insideLeft",
                offset: 0,
                style: { textAnchor: "middle", fill: "#555" },
              }}
            />{" "}
            <Tooltip />
            <Legend content={CustomFuelLegend} />
            <Bar
              dataKey={`${year1} hazardous`}
              stackId="a"
              fill="#f46d43"
              name="Hazardous waste"
            ></Bar>
            <Bar
              dataKey={`${year1} nonHazardous`}
              stackId="a"
              fill="#fee08b"
              name="Non-Hazardous waste"
            ></Bar>
            <Bar
              dataKey={`${year2} hazardous`}
              stackId="b"
              fill="#f46d43"
              t
            ></Bar>
            <Bar
              dataKey={`${year2} nonHazardous`}
              stackId="b"
              fill="#fee08b"
            ></Bar>
          </BarChart>
        </ResponsiveContainer>
      )}
      {!activeMode && (
        <div>
          <DataTable
            ref={dt}
            value={HaNData[year1].map((item, index) => ({
              ...item,
              [`hazardous_${year2}`]: HaNData[year2][index].hazardous,
              [`nonHazardous_${year2}`]: HaNData[year2][index].nonHazardous,
            }))}
            tableClassName="font-lato"
            style={{ width: "100%" }}
          >
            <Column
              header="Month"
              style={{ minWidth: "8%" }}
              field="month"
              emptyMessage="No Assignment(s)"
            />
            <Column
              header={`Hazardous - ${year1}`}
              style={{ minWidth: "8%" }}
              field="hazardous"
              emptyMessage="No Assignment(s)"
            />
            <Column
              header={`Non-Hazardous - ${year1}`}
              style={{ minWidth: "8%" }}
              field="nonHazardous"
              emptyMessage="No Assignment(s)"
            />
            <Column
              header={`Mineral Fuel - ${year2}`}
              style={{ minWidth: "8%" }}
              field={`hazardous_${year2}`}
              emptyMessage="No Assignment(s)"
            />
            <Column
              header={`Biofuel Blend - ${year2}`}
              style={{ minWidth: "8%" }}
              field={`nonHazardous_${year2}`}
              emptyMessage="No Assignment(s)"
            />
          </DataTable>
        </div>
      )}
    </div>
  );
};
export default HazardousAndNonHazardousChart;

import React, { useEffect, useRef, useState } from "react";
import { Dialog } from "primereact/dialog";
import { But<PERSON> } from "primereact/button";
import { useSelector } from "react-redux";
import moment from "moment";
import { DateTime } from "luxon";
import Swal from "sweetalert2";
import APIServices from "../service/APIService";
import ConsolidateDialog from "./ConsolidateDialog";
import { API } from "../constants/api_url";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";

const statusColors = {
    Completed: "#29C76F",
    "In Progress": "#F5C37B",
    "Not Started": "#CCCED5",
    Blocked: "#fff",
    "Rejected": 'orangered'
};

const QualitativeResponse = ({ data, categoryData, refresh,siteList }) => {
    console.log(data)
    const login_data = useSelector((state) => state.user.userdetail);
    const [visible, setVisible] = useState(false);
    const [selectedItem, setSelectedItem] = useState(null);
    const [formData, setFormData] = useState({});
    const userList = useSelector(state => state.userlist.userList);
    const tvsExtUserList = useSelector(state => state.userlist.tvsExtUserList);
    const userList_ = [...userList, ...tvsExtUserList];
    // State for consolidate dialog
    const [consolidateDialogVisible, setConsolidateDialogVisible] = useState(false);
    const [selectedConsolidateSection, setSelectedConsolidateSection] = useState({
        assignmentId: null,
        sectionId: null
    });

    const didMount = useRef(false);

    useEffect(() => {
        if (didMount.current) {
            if (!consolidateDialogVisible) {
                refresh();
            }
        } else {
            didMount.current = true;
        }
    }, [consolidateDialogVisible]);

    const rejectSubmission = () => {
        console.log(selectedItem)
        Swal.fire({
            title: 'Return Submission with Comments',
            html: `
                <div style="text-align: left;">
                    <label for="rejection-comments" style="display: block; margin-bottom: 8px; font-weight: bold;">
                        Comments (Required):
                    </label>
                    <textarea
                        id="rejection-comments"
                        class="swal2-textarea"
                        placeholder="Please provide comments for returning this submission..."
                        style="width: 100%; min-height: 100px; resize: vertical;"
                        required
                    ></textarea>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Return Submission',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#dc3545',
            preConfirm: () => {
                const comments = document.getElementById('rejection-comments').value.trim();
                if (!comments) {
                    Swal.showValidationMessage('Comments are required');
                    return false;
                }
                return comments;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const comments = result.value;

                const rejectionData = {
                    reporterId: selectedItem.reporterId.toString(),
                    rejected_by: login_data.id,
                    type: 0,
                    reject: 1,
                    rejected_on: DateTime.utc().toISO(),
                    return_remarks: [{
                        user_id: login_data.id,
                        user_type: 2, // Reviewer type
                        remarks: comments,
                        created_on: DateTime.utc().toISO()
                    }]
                };

                // Call the reject API
                APIServices.post(API.Reject_QualitativeAssignment(selectedItem.id), rejectionData)
                    .then((response) => {
                        if (response.data.status) {
                            Swal.fire({
                                title: 'Success!',
                                text: 'Submission has been returned with comments.',
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                setVisible(false);
                                refresh(); // Refresh the data
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.data.message || 'Failed to return submission.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch((error) => {
                        console.error('Error rejecting submission:', error);
                        Swal.fire({
                            title: 'Error!',
                            text: 'An error occurred while returning the submission. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    });
            }
        });
    }

    const getUser = (id) => {
        let user_name = 'Not Found'
        let index = userList_.findIndex(i => i.id === Number(id))
        if (index !== -1) {
            user_name = userList_[index].information.empname
        }
        return user_name
    }
    const openDialog = (item) => {
        const prefilled = {};
        item.form?.forEach((field) => {
            if (field.type === "checkbox-group") {
                prefilled[field.name] = Array.isArray(field.value) ? field.value : [];
            } else {
                prefilled[field.name] = field.value || "";
            }
        });

        setSelectedItem(item);
        setFormData(prefilled);
        setVisible(true);
    };

    return (
        <div className="p-4 border rounded-lg shadow-sm bg-white mb-4">
            <h2 className="fs-4 fw-bold mb-4">{data[0]?.title || "Environment"}</h2>


            <div style={{ overflowX: "auto", maxWidth: "100%" }}>
                <table className="table table-bordered" style={{ minWidth: "1000px", width: "100%" }}>
                    <tbody>
                        {(() => {
                            // Group data by topic (subHeading)
                            const groupedByTopic = {};
                            data.forEach((env) => {
                                if (!groupedByTopic[env.subHeading]) {
                                    groupedByTopic[env.subHeading] = [];
                                }
                                groupedByTopic[env.subHeading].push(env);
                            });

                            // Render each topic group
                            return Object.entries(groupedByTopic).map(([topicName, topicEntities]) => {
                                // Get all section names for this topic
                                const allSectionNames = new Set();
                                topicEntities.forEach(env => {
                                    Object.values(env.data).forEach((userSections) => {
                                        Object.keys(userSections).forEach((section) => allSectionNames.add(section));
                                    });
                                });

                                // Topic header row
                                const topicHeaderRow = (
                                    <tr key={`topic-header-${topicName}`}>
                                        <td colSpan={2} className="text-white fw-bold px-4 py-3 border" style={{ position: "sticky", background:'#315975', top: 0, zIndex: 5 }}>
                                            <h4 className="mb-0">{topicName}</h4>
                                        </td>
                                    </tr>
                                );

                                // Entity rows for this topic
                                const entityRows = topicEntities.map((env, entityIndex) => {

                                    const rows = Object.entries(env.data).map(([userId, sections], userIndex) => userId !== 'Consolidate' ? (

                                        <tr key={`${topicName}-${entityIndex}-${userId}`}>
                                            <td
                                                className="p-4 align-top bg-white"
                                                style={{ width: "300px", minWidth: "250px", position: "sticky", left: 0, zIndex: 2 }}
                                            >
                                                <p className="fw-semibold fs-5">Reporter: {getUser(userId)}</p>
                                                <p className="text-muted">{env.location}</p>
                                            </td>
                                            <td className="p-0">
                                                <div style={{ display: "flex", overflowX: "auto", whiteSpace: "nowrap", maxWidth: "100%" }}>
                                                    {(() => {
                                                        // Find all section names from the topic structure in categoryData
                                                        const currentTopic = env.subHeading;
                                                        const currentCategory = env.title;

                                                        // Find the matching category and topic in categoryData
                                                        const matchedCategory = categoryData?.find(cat => cat.name === currentCategory);
                                                        const matchedTopic = matchedCategory?.qTopics?.find(topic => topic.name === currentTopic);

                                                        // Get all section names from the topic structure
                                                        const topicSectionNames = matchedTopic?.qSections?.map(section => section.name) || [];

                                                        // Combine section names from both data and allSectionNames
                                                        const combinedSectionNames = new Set([...allSectionNames, ...topicSectionNames]);

                                                        return [...combinedSectionNames].map((sectionName, i) => {
                                                            const item = sections[sectionName] || {
                                                                name: sectionName,
                                                                status: "Blocked",
                                                                dueDate: "",
                                                                isLocked: false,
                                                                form: [],
                                                                sectionId: matchedTopic?.qSections?.find(s => s.name === sectionName)?.id
                                                            };
                                                            const isBlocked = item.status === "Blocked";

                                                            return (
                                                                <div
                                                                    key={i}
                                                                    className={`d-flex flex-column border flex-shrink-0 ${isBlocked ? "text-muted" : "hover-effect"}`}
                                                                    style={{
                                                                        width: "250px",
                                                                        minWidth: "250px",
                                                                        cursor: isBlocked ? "not-allowed" : "pointer",
                                                                        opacity: isBlocked ? 0.8 : 1,
                                                                        pointerEvents: isBlocked ? "none" : "auto",
                                                                    }}
                                                                    onClick={() => !isBlocked && openDialog(item)}
                                                                >
                                                                    <div
                                                                        className="w-100"
                                                                        style={{ height: "10px", backgroundColor: item.rejected ? statusColors.Rejected : statusColors[item.status] || "#fff" }}
                                                                    />
                                                                    <div className="p-3 flex-grow-1">
                                                                        <h5 className="fw-bold text-wrap">{item.name}</h5>
                                                                        {!isBlocked && (
                                                                            <p className="text-muted mt-2">Due date:<br /> {item.dueDate}</p>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            );
                                                        });
                                                    })()}
                                                </div>
                                            </td>
                                        </tr>
                                    ) : null).filter(x => x);

                                    const sectionMap = {};
                                    Object.entries(env.data).forEach(([userId, sections]) => {
                                        Object.entries(sections).forEach(([sectionName, sectionData]) => {

                                            if (!sectionMap[sectionName]) sectionMap[sectionName] = [];
                                            sectionMap[sectionName].push({
                                                ...sectionData,
                                                userId,
                                                location: env.location,
                                                rawResponse: sectionData.form?.reduce((acc, field) => {
                                                    if (field.type === "checkbox-group") {
                                                        acc[field.name] = Array.isArray(field.value) ? field.value : [];
                                                    } else {
                                                        acc[field.name] = field.value || "";
                                                    }
                                                    return acc;
                                                }, {}),
                                                form: sectionData.form || [],
                                            });
                                        });
                                    });

                                    const consolidatedRowKey = `${env.subHeading}-${entityIndex}`;
                                    const isLastOfGroup = entityIndex === topicEntities.length - 1;

                                    // Determine overall consolidated status for the entire row
                                    let overallConsolidatedStatus = "Not Started";
                                    const allResponses = [];

                                    // Collect all individual responses across all sections
                                    Object.values(sectionMap).forEach(responses => {
                                        responses.filter(x => x.userId !== "Consolidate").forEach(resp => {
                                            allResponses.push(resp);
                                        });
                                    });

                                    if (allResponses.length > 0) {
                                        // Check if all responses are Completed (green)
                                        const allCompleted = allResponses.every(resp => resp.status === "Completed");

                                        // Check if any response is In Progress (yellow)
                                        const anyInProgress = allResponses.some(resp => resp.status === "In Progress");

                                        if (allCompleted) {
                                            overallConsolidatedStatus = "Completed";
                                        } else if (anyInProgress) {
                                            overallConsolidatedStatus = "In Progress";
                                        }
                                    }

                                    // Set background color based on status
                                    const consolidatedRowBgColor =
                                        overallConsolidatedStatus === "Completed" ? "#e6f7ef" :
                                            overallConsolidatedStatus === "In Progress" ? "#fff8e6" : "#f5f5f5";

                                    const consolidatedRow = isLastOfGroup ? (
                                        <tr key={`consolidated-${consolidatedRowKey}`}>
                                            <td
                                                className="p-4 align-top fw-bold"
                                                style={{
                                                    width: "300px",
                                                    minWidth: "250px",
                                                    position: "sticky",
                                                    left: 0,
                                                    zIndex: 2,
                                                    backgroundColor: consolidatedRowBgColor
                                                }}
                                            >
                                                <h3 className="fw-semibold fs-5">Consolidated Report</h3>
                                                <p className="text-muted">Across all users</p>
                                            </td>
                                            <td className="p-0" style={{ backgroundColor: consolidatedRowBgColor }}>
                                                <div style={{ display: "flex", overflowX: "auto", whiteSpace: "nowrap", maxWidth: "100%" }}>
                                                    {(() => {
                                                        // Find all section names from the topic structure in categoryData
                                                        const currentTopic = env.subHeading;
                                                        const currentCategory = env.title;

                                                        // Find the matching category and topic in categoryData
                                                        const matchedCategory = categoryData?.find(cat => cat.name === currentCategory);
                                                        const matchedTopic = matchedCategory?.qTopics?.find(topic => topic.name === currentTopic);

                                                        // Get all section names from the topic structure
                                                        const topicSectionNames = matchedTopic?.qSections?.map(section => section.name) || [];

                                                        // Combine section names from both data and topic structure
                                                        const combinedSectionNames = new Set([...Object.keys(sectionMap), ...topicSectionNames]);

                                                        return [...combinedSectionNames].map((sectionName, i) => {
                                                            // Get responses for this section if they exist
                                                            const responses = sectionMap[sectionName] || [];

                                                            // Find the consolidate response
                                                            const consolidateResponse = responses.find(x => x?.userId === "Consolidate") || {};
                                                            console.log("consolidateResponse", consolidateResponse, responses);
                                                            // Get section ID from the topic structure
                                                            const sectionId = matchedTopic?.qSections?.find(s => s.name === sectionName)?.id;

                                                            // If no consolidate response exists, create a placeholder
                                                            if (!consolidateResponse.userId) {
                                                                consolidateResponse.status = "Blocked";
                                                                consolidateResponse.name = sectionName;
                                                                consolidateResponse.sectionId = sectionId;
                                                            }

                                                            // Check if there are any non-rejected user responses
                                                            const individualResponses = responses.filter(x => x?.userId !== "Consolidate");
                                                            const nonRejectedResponses = individualResponses.filter(resp => !resp.reject || resp.reject === 0);
                                                            const hasValidUserResponses = nonRejectedResponses.length > 0;

                                                            // Determine consolidated status based on consolidate response, not individual responses
                                                            let consolidatedStatus = "Not Started";

                                                            if (consolidateResponse.userId === "Consolidate" && consolidateResponse.rawResponse) {
                                                                // Check consolidate response completion
                                                                const consolidateAnswers = Object.values(consolidateResponse.rawResponse || {});
                                                                const answeredQuestions = consolidateAnswers.filter(val =>
                                                                    val !== null && val !== undefined && val !== "" &&
                                                                    (typeof val !== 'object' || (Array.isArray(val) && val.length > 0))
                                                                );
                                                                const totalQuestions = consolidateAnswers.length;

                                                                if (answeredQuestions.length === totalQuestions && totalQuestions > 0) {
                                                                    consolidatedStatus = "Completed";
                                                                } else if (answeredQuestions.length > 0) {
                                                                    consolidatedStatus = "In Progress";
                                                                }
                                                            }

                                                            // Show consolidate button only if there are valid user responses OR consolidate response exists
                                                            const showConsolidateButton = hasValidUserResponses || consolidateResponse.userId === "Consolidate";

                                                            return (
                                                                <div
                                                                    key={i}
                                                                    className={`d-flex flex-column border flex-shrink-0 ${!consolidateResponse.assignmentId ? "text-muted" : "hover-effect"}`}
                                                                    style={{
                                                                        width: "250px",
                                                                        minWidth: "250px",
                                                                        cursor: showConsolidateButton ? "pointer" : 'default',
                                                                        opacity: !showConsolidateButton ? 0.8 : 1,
                                                                        backgroundColor: showConsolidateButton ?
                                                                            (consolidatedStatus === "Completed" ? "#e6f7ef" :
                                                                                consolidatedStatus === "In Progress" ? "#fff8e6" : "#f5f5f5") : "#fff"
                                                                    }}
                                                                >
                                                                    <div className="w-100" style={{ height: "10px", backgroundColor: showConsolidateButton ? statusColors[consolidatedStatus] : "#fff" }}></div>
                                                                    <div className="p-3 flex-grow-1 flex justify-content-between flex-column">
                                                                        <h5 className="fw-bold text-wrap">{sectionName}</h5>
                                                                        {showConsolidateButton && <div className="flex justify-content-between align-items-center">
                                                                            <Button
                                                                                style={{ padding: '5px 0px' }}
                                                                                text
                                                                                label="Consolidate"
                                                                                onClick={(e) => {
                                                                                    e.stopPropagation();
                                                                                    setSelectedConsolidateSection({
                                                                                        assignmentId: consolidateResponse.assignmentId,
                                                                                        sectionId: consolidateResponse.sectionId
                                                                                    });
                                                                                    setConsolidateDialogVisible(true);
                                                                                }}
                                                                            />
                                                                            <div>{Object.values(consolidateResponse?.rawResponse || {}).filter(
                                                                                val => val !== null && val !== undefined && val !== "" &&
                                                                                (typeof val !== 'object' || (Array.isArray(val) && val.length > 0))
                                                                            ).length + '/' + Object.keys(consolidateResponse?.rawResponse || {})?.length}</div>
                                                                        </div>}
                                                                    </div>
                                                                </div>
                                                            );
                                                        });
                                                    })()}
                                                </div>
                                            </td>
                                        </tr>
                                    ) : null;

                                    const documentRow = isLastOfGroup ? (
                                        <tr key={`documents-${consolidatedRowKey}`}>
                                            <td
                                                className="p-4 align-top bg-white fw-bold"
                                                style={{ width: "300px", minWidth: "250px", position: "sticky", left: 0, zIndex: 2 }}
                                            >
                                                <h3 className="fw-semibold fs-5">Uploaded Documents</h3>
                                                <p className="text-muted">All file attachments</p>
                                            </td>
                                            <td className="p-0">
                                                <div className="p-3" style={{ overflowX: "auto" }}>
                                                    <table className="table table-sm table-bordered mb-0" style={{ maxWidth: "960px" }}>
                                                        <thead className="bg-light">
                                                            <tr>
                                                                <th>Section</th>
                                                                <th>User</th>
                                                                <th>Label</th>
                                                                <th>File</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {Object.entries(sectionMap).flatMap(([sectionName, responses]) =>
                                                                responses.flatMap((res, idx) => {
                                                                    return res.form
                                                                        ?.filter(field => field.type === "file" && !!field.value)
                                                                        .map((fileField, i) => (
                                                                            <tr key={`${sectionName}-${idx}-${i}`}>
                                                                                <td>{sectionName}</td>
                                                                                <td>{Number(res.userId) === 'number' ? getUser(res.userId) : res.userId}</td>
                                                                                <td>{fileField.label}</td>
                                                                                <td>
                                                                                    <a href={fileField?.value?.includes('api.eisqr') ? fileField?.value : API.Docs + fileField?.value} rel="noopener noreferrer">
                                                                                        {fileField?.value?.split("/").pop()}
                                                                                    </a>
                                                                                </td>
                                                                            </tr>
                                                                        )) || [];
                                                                })
                                                            )}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>
                                    ) : null;

                                    return [...rows, consolidatedRow, documentRow].filter(x => x);
                                });

                                // Return topic header + all entity rows for this topic
                                return [topicHeaderRow, ...entityRows.flat()];
                            }).flat(); // Flatten to get a single array of all rows
                        })()}
                    </tbody>
                </table>
            </div>

            <Dialog header={selectedItem?.name} visible={visible} style={{ width: "60vw" }} onHide={() => setVisible(false)}>
                <div className="p-3">
                    {selectedItem?.responses ? (
                        <>
                            <h5 className="fw-bold mb-3">All User Responses</h5>
                            {selectedItem.responses.map((userItem, idx) => (
                                userItem.userId !== 'Consolidate' &&
                                <div key={idx} className="mb-4 border rounded p-3 bg-light">
                                    <h6 className="fw-bold mb-3">
                                        Reporter: {getUser(userItem.userId)} | Location: {userItem.location}
                                    </h6>

                                    {userItem.rawResponse &&
                                        Object.entries(userItem.rawResponse).map(([key, value], i) => {
                                            if (key === "status") return null;

                                            const formField = userItem.form?.find(f => f.name === key);
                                            const label = formField?.label || key;
                                            const type = formField?.type;

                                            let displayValue = value;

                                            if (type === "radio-group") {
                                                const option = formField.values?.find(opt => opt.value === value);
                                                displayValue = option?.label || value;
                                            }

                                            if (type === "checkbox-group" && Array.isArray(value)) {
                                                displayValue = value
                                                    .map(v => formField.values?.find(opt => opt.value === v)?.label || v)
                                                    .join(", ");
                                            }

                                            if (type === "file" && typeof value === "string" && value.trim() !== "") {
                                                displayValue = (
                                                    <a href={value?.includes('api.eisqr') ? value : API.Docs + value} rel="noopener noreferrer">
                                                        {value}
                                                    </a>
                                                );
                                            }

                                            if (type === "tableadd" && Array.isArray(value) && value.length > 0) {
                                                displayValue = (
                                                    <div className="border rounded p-2 bg-white mt-2">
                                                        <DataTable
                                                            value={value}
                                                            showGridlines
                                                            className="p-datatable-sm"
                                                            style={{ width: '100%' }}
                                                            scrollable
                                                            scrollHeight="200px"
                                                        >
                                                            {formField.headers && formField.headers.map((header, headerIndex) => (
                                                                <Column
                                                                    key={headerIndex}
                                                                    field={header}
                                                                    header={header}
                                                                    body={(rowData) => {
                                                                        const cellData = rowData[header];
                                                                        if (!cellData || !cellData.data) return '-';

                                                                        const cellValue = cellData.data.value;

                                                                        // Handle different field types for display
                                                                        switch (cellData.type) {
                                                                            case 1: // text
                                                                            case 2: // textarea
                                                                            case 3: // number
                                                                                return cellValue || '-';
                                                                            case 4: // dropdown
                                                                                if (cellData.data.values && cellValue !== null && cellValue !== undefined) {
                                                                                    const selectedOption = cellData.data.values.find(opt => opt.value === cellValue);
                                                                                    return selectedOption ? selectedOption.label : cellValue;
                                                                                }
                                                                                return cellValue || '-';
                                                                            case 5: // label
                                                                                return cellData.data.label || '-';
                                                                            case 6: // date
                                                                                return cellValue ? moment(cellValue).format('DD-MM-YYYY') : '-';
                                                                            default:
                                                                                return cellValue || '-';
                                                                        }
                                                                    }}
                                                                />
                                                            ))}
                                                        </DataTable>
                                                    </div>
                                                );
                                            }

                                            return (
                                                <div key={i} className="mb-3">
                                                    <label className="fw-semibold d-block">{label}</label>
                                                    <div className="text-muted">{displayValue || "-"}</div>
                                                </div>
                                            );
                                        })}
                                </div>
                            ))}
                        </>
                    ) : (
                        <>
                            <label className="fw-bold  col-8">{selectedItem?.reporter}</label>
                       
                           {selectedItem?.rejected === 1 && <div className="col-12 grid m-0 p-1  fw-7 mb-3 ">
                                <label className="fw-bold  col-6">Remarks <span className="mandatory" >{ selectedItem?.rejection_remarks}</span> </label>
                                <label className='col-6'> Rejected on : {selectedItem?.rejected_on ? moment(selectedItem?.rejected_on).format('DD-MMM-YYYY') : ''}  </label>

                                
                            </div>}
                            {selectedItem?.form?.map((field, i) => (
                                <div key={i} className="mt-3">
                                    <label className="fw-bold">{field.label}</label>

                                    {["text", "textarea"].includes(field.type) && (
                                        <div className="mt-2 text-muted border rounded p-2 bg-light">
                                            {formData[field.name] || "-"}
                                        </div>
                                    )}

                                    {field.type === "radio-group" && (
                                        <div className="mt-2 text-muted border rounded p-2 bg-light">
                                            {(() => {
                                                const selectedValue = formData[field.name];
                                                const selectedLabel = field.values.find(v => v.value === selectedValue)?.label;
                                                return selectedLabel || "-";
                                            })()}
                                        </div>
                                    )}

                                    {field.type === "checkbox-group" && (
                                        <div className="mt-2 text-muted border rounded p-2 bg-light">
                                            {(formData[field.name] || [])
                                                .map(val => field.values.find(opt => opt.value === val)?.label || val)
                                                .join(", ") || "-"}
                                        </div>
                                    )}

                                    {field.type === "file" && (
                                        <div className="mt-2">
                                            {formData[field.name] ? (
                                                <a href={formData[field.name]?.includes('api.eisqr') ? formData[field.name] : API.Docs + formData[field.name]} rel="noopener noreferrer">
                                                    {formData[field.name]}
                                                </a>
                                            ) : (
                                                <span className="text-muted">-</span>
                                            )}
                                        </div>
                                    )}

                                    {field.type === "tableadd" && (
                                        <div className="mt-2">
                                            {formData[field.name] && formData[field.name].length > 0 ? (
                                                <div className="border rounded p-2 bg-light">
                                                    <DataTable
                                                        value={formData[field.name]}
                                                        showGridlines
                                                        className="p-datatable-sm"
                                                        style={{ width: '100%' }}
                                                        scrollable
                                                        scrollHeight="300px"
                                                    >
                                                        {field.headers && field.headers.map((header, headerIndex) => (
                                                            <Column
                                                                key={headerIndex}
                                                                field={header}
                                                                header={header}
                                                                body={(rowData) => {
                                                                    const cellData = rowData[header];
                                                                    if (!cellData || !cellData.data) return '-';

                                                                    const value = cellData.data.value;

                                                                    // Handle different field types for display
                                                                    switch (cellData.type) {
                                                                        case 1: // text
                                                                        case 2: // textarea
                                                                        case 3: // number
                                                                            return value || '-';
                                                                        case 4: // dropdown
                                                                            if (cellData.data.values && value !== null && value !== undefined) {
                                                                                const selectedOption = cellData.data.values.find(opt => opt.value === value);
                                                                                return selectedOption ? selectedOption.label : value;
                                                                            }
                                                                            return value || '-';
                                                                        case 5: // label
                                                                            return cellData.data.label || '-';
                                                                        case 6: // date
                                                                            return value ? moment(value).format('DD-MM-YYYY') : '-';
                                                                        default:
                                                                            return value || '-';
                                                                    }
                                                                }}
                                                            />
                                                        ))}
                                                    </DataTable>
                                                </div>
                                            ) : (
                                                <div className="mt-2 text-muted border rounded p-2 bg-light">
                                                    No data available
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            ))}

                            {!selectedItem?.rejected && <div className="mt-4 flex justify-content-end">
                                <Button variant="primary" label={'Return with Comments'} onClick={rejectSubmission} />

                            </div>}
                        </>
                    )}
                </div>
            </Dialog>

            {/* Consolidate Dialog */}
            <ConsolidateDialog
            rawSiteList={siteList}
                visible={consolidateDialogVisible}
                onHide={() => setConsolidateDialogVisible(false)}
                assignmentId={selectedConsolidateSection.assignmentId}
                sectionId={selectedConsolidateSection.sectionId}
            />
        </div>
    );
};

export default QualitativeResponse;

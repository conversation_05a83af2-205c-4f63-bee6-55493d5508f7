import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { Checkbox } from "@mui/material";

const dummyData = {
  scope1: { actual: 100, target: 120 },
  scope2: { actual: 80, target: 100 },
  scope3: { actual: 60, target: 90 },
};

const BulletGraph = () => {
  const [visibleSeries, setVisibleSeries] = useState({
    scope1: true,
    scope2: true,
    scope3: true,
  });
  const chartRef = useRef(null);

  useEffect(() => {
    renderBulletGraph();
  }, [visibleSeries]);

  const renderBulletGraph = () => {
    const width = 600;
    const height = 120;
    const margin = { top: 20, right: 20, bottom: 30, left: 40 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    // Clear previous chart content
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const categories = ["scope1", "scope2", "scope3"];
    const colorScale = d3
      .scaleOrdinal()
      .domain(categories)
      .range(["#e74c3c", "#f39c12", "#16a085"]);

    categories.forEach((category, index) => {
      const data = dummyData[category];
      const actual = data.actual;
      const target = data.target;

      // Create the bar for actual emissions
      svg
        .append("rect")
        .attr("x", 0)
        .attr("y", index * (chartHeight / categories.length))
        .attr("width", (actual / 200) * chartWidth) // scale the value relative to the max possible value (200 in this case)
        .attr("height", chartHeight / categories.length - 10)
        .attr("fill", colorScale(category));

      // Create the target line (vertical line for target emissions)
      svg
        .append("line")
        .attr("x1", (target / 200) * chartWidth)
        .attr(
          "y1",
          index * (chartHeight / categories.length) +
            (chartHeight / categories.length - 10) / 2
        )
        .attr("x2", (target / 200) * chartWidth)
        .attr(
          "y2",
          index * (chartHeight / categories.length) +
            (chartHeight / categories.length - 10) / 2
        )
        .attr("stroke", "#000")
        .attr("stroke-width", 3);

      // Add labels for actual emissions and target
      svg
        .append("text")
        .attr("x", (actual / 200) * chartWidth + 5)
        .attr(
          "y",
          index * (chartHeight / categories.length) +
            (chartHeight / categories.length - 10) / 2
        )
        .attr("dy", "0.35em")
        .text(`${actual}`)
        .attr("font-size", "12px")
        .attr("fill", "#333");

      svg
        .append("text")
        .attr("x", (target / 200) * chartWidth + 5)
        .attr(
          "y",
          index * (chartHeight / categories.length) +
            (chartHeight / categories.length - 10) / 2
        )
        .attr("dy", "0.35em")
        .text(`Target: ${target}`)
        .attr("font-size", "12px")
        .attr("fill", "#333");
    });
  };

  const handleCheckboxChange = (key) => {
    setVisibleSeries((prevState) => ({
      ...prevState,
      [key]: !prevState[key],
    }));
  };

  return (
    <div>
      <div
        style={{
          fontFamily: "Lato",
          fontSize: "16px",
          fontWeight: 700,
          lineHeight: "19.2px",
          textAlign: "left",
          marginBottom: "10px",
        }}
      >
        Bullet Graph for Emissions
        <div style={{ fontWeight: 200, fontSize: "14px" }}>
          Compare actual emissions against target emissions for Scope 1, Scope
          2, and Scope 3.
        </div>
      </div>
      <div ref={chartRef} style={{ textAlign: "center" }} />

      {/* Legends */}
      <div style={{ textAlign: "center", marginTop: "20px" }}>
        <div style={{ display: "inline-block", marginRight: "20px" }}>
          <Checkbox
            checked={visibleSeries["scope1"]}
            onChange={() => handleCheckboxChange("scope1")}
            style={{
              color: "#e74c3c",
              marginRight: 4,
              fontSize: "20px",
            }}
          />
          <span style={{ color: "#555", fontSize: "14px" }}>Scope 1</span>
        </div>
        <div style={{ display: "inline-block", marginRight: "20px" }}>
          <Checkbox
            checked={visibleSeries["scope2"]}
            onChange={() => handleCheckboxChange("scope2")}
            style={{
              color: "#f39c12",
              marginRight: 4,
              fontSize: "20px",
            }}
          />
          <span style={{ color: "#555", fontSize: "14px" }}>Scope 2</span>
        </div>
        <div style={{ display: "inline-block", marginRight: "20px" }}>
          <Checkbox
            checked={visibleSeries["scope3"]}
            onChange={() => handleCheckboxChange("scope3")}
            style={{
              color: "#16a085",
              marginRight: 4,
              fontSize: "20px",
            }}
          />
          <span style={{ color: "#555", fontSize: "14px" }}>Scope 3</span>
        </div>
      </div>
    </div>
  );
};

export default BulletGraph;

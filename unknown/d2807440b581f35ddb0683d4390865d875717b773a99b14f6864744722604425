import React, { useState, useRef } from "react";
import { But<PERSON> } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { CustomLegend } from "./Dashboard";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer,
  LabelList,
} from "recharts";

export const scopeData = {
  2019: [
    { month: "Jan", category1: 30, category12: 35, category11: 50 },
    { month: "Feb", category1: 140, category12: 35, category11: 60 },
    { month: "Mar", category1: 50, category12: 35, category11: 30 },
    { month: "Apr", category1: 80, category12: 35, category11: 20 },
    { month: "May", category1: 100, category12: 35, category11: 30 },
    { month: "Jun", category1: 30, category12: 35, category11: 90 },
    { month: "Jul", category1: 30, category12: 35, category11: 50 },
    { month: "Aug", category1: 50, category12: 35, category11: 60 },
    { month: "Sep", category1: 20, category12: 35, category11: 70 },
    { month: "Oct", category1: 40, category12: 35, category11: 40 },
    { month: "Nov", category1: 10, category12: 35, category11: 20 },
    { month: "Dec", category1: 10, category12: 35, category11: 100 },
  ],
  2020: [
    { month: "Jan", category1: 75, category12: 35, category11: 43 },
    { month: "Feb", category1: 32, category12: 35, category11: 34 },
    { month: "Mar", category1: 45, category12: 35, category11: 64 },
    { month: "Apr", category1: 23, category12: 35, category11: 54 },
    { month: "May", category1: 10, category12: 35, category11: 34 },
    { month: "Jun", category1: 42, category12: 35, category11: 75 },
    { month: "Jul", category1: 34, category12: 35, category11: 54 },
    { month: "Aug", category1: 23, category12: 35, category11: 42 },
    { month: "Sep", category1: 15, category12: 35, category11: 42 },
    { month: "Oct", category1: 65, category12: 35, category11: 30 },
    { month: "Nov", category1: 23, category12: 35, category11: 43 },
    { month: "Dec", category1: 77, category12: 35, category11: 32 },
  ],
  2021: [
    { month: "Jan", category1: 30, category12: 35, category11: 80 },
    { month: "Feb", category1: 28, category12: 35, category11: 62 },
    { month: "Mar", category1: 38, category12: 35, category11: 52 },
    { month: "Apr", category1: 60, category12: 35, category11: 10 },
    { month: "May", category1: 45, category12: 35, category11: 85 },
    { month: "Jun", category1: 30, category12: 35, category11: 50 },
    { month: "Jul", category1: 45, category12: 35, category11: 45 },
    { month: "Aug", category1: 60, category12: 35, category11: 30 },
    { month: "Sep", category1: 35, category12: 35, category11: 45 },
    { month: "Oct", category1: 25, category12: 35, category11: 35 },
    { month: "Nov", category1: 40, category12: 35, category11: 40 },
    { month: "Dec", category1: 28, category12: 35, category11: 72 },
  ],
  2022: [
    { month: "Jan", category1: 42, category12: 35, category11: 23 },
    { month: "Feb", category1: 42, category12: 35, category11: 12 },
    { month: "Mar", category1: 42, category12: 35, category11: 33 },
    { month: "Apr", category1: 66, category12: 35, category11: 12 },
    { month: "May", category1: 20, category12: 35, category11: 64 },
    { month: "Jun", category1: 25, category12: 35, category11: 22 },
    { month: "Jul", category1: 60, category12: 35, category11: 32 },
    { month: "Aug", category1: 33, category12: 35, category11: 25 },
    { month: "Sep", category1: 55, category12: 35, category11: 40 },
    { month: "Oct", category1: 20, category12: 35, category11: 67 },
    { month: "Nov", category1: 45, category12: 35, category11: 33 },
    { month: "Dec", category1: 25, category12: 35, category11: 55 },
  ],
  2023: [
    { month: "Jan", category1: 23, category12: 35, category11: 50 },
    { month: "Feb", category1: 42, category12: 35, category11: 52 },
    { month: "Mar", category1: 53, category12: 35, category11: 45 },
    { month: "Apr", category1: 76, category12: 35, category11: 0 },
    { month: "May", category1: 32, category12: 35, category11: 85 },
    { month: "Jun", category1: 62, category12: 35, category11: 40 },
    { month: "Jul", category1: 54, category12: 35, category11: 35 },
    { month: "Aug", category1: 65, category12: 35, category11: 20 },
    { month: "Sep", category1: 21, category12: 35, category11: 35 },
    { month: "Oct", category1: 64, category12: 35, category11: 15 },
    { month: "Nov", category1: 23, category12: 35, category11: 50 },
    { month: "Dec", category1: 53, category12: 35, category11: 27 },
  ],
  2024: [
    { month: "Jan", category1: 43, category12: 35, category11: 45 },
    { month: "Feb", category1: 75, category12: 35, category11: 48 },
    { month: "Mar", category1: 42, category12: 35, category11: 40 },
    { month: "Apr", category1: 75, category12: 35, category11: 20 },
    { month: "May", category1: 34, category12: 35, category11: 70 },
    { month: "Jun", category1: 65, category12: 35, category11: 35 },
    { month: "Jul", category1: 23, category12: 35, category11: 30 },
    { month: "Aug", category1: 54, category12: 35, category11: 15 },
    { month: "Sep", category1: 22, category12: 35, category11: 30 },
    { month: "Oct", category1: 75, category12: 35, category11: 10 },
    { month: "Nov", category1: 12, category12: 35, category11: 45 },
    { month: "Dec", category1: 65, category12: 35, category11: 15 },
  ],
};

const CustomScopeLegend = (props) => {
  const { payload } = props;
  return (
    <ul
      style={{
        display: "flex",
        listStyleType: "none",
        justifyContent: "center",
        padding: 0,
      }}
    >
      {payload.map(
        (entry, index) =>
          index <= 2 && (
            <li
              key={`item-${index}`}
              style={{
                color: entry.color,

                marginRight: "5px",
              }}
            >
              <span
                style={{
                  color: entry.color,
                  marginRight: 4,
                  fontSize: "20px",
                }}
              >
                ■
              </span>
              <span style={{ color: "#555", fontSize: "14px" }}>
                {entry.value}
              </span>
            </li>
          )
      )}
    </ul>
  );
};

export const Scope3Chart = () => {
  const [activeMode, setActiveMode] = useState(true);
  const dt = useRef(null);
  const [year1, setYear1] = useState(2019);
  const [year2, setYear2] = useState(2020);

  const combinedData = scopeData[year1].map((item, index) => ({
    month: item.month,
    [`${year1} category1`]: item.category1,
    [`${year1} category11`]: item.category11,
    [`${year1} category12`]: item.category12,
    [`${year2} category1`]: scopeData[year2][index].category1,
    [`${year2} category11`]: scopeData[year2][index].category11,
    [`${year2} category12`]: scopeData[year2][index].category12,
  }));
  return (
    <>
      <div
        style={{
          width: "100%",
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        <div
          style={{
            fontFamily: "Lato",
            fontSize: "16px",
            fontWeight: 700,
            lineHeight: "19.2px",
            textAlign: "left",
            margin: "18px 10px 18px 10px",
          }}
        >
          Scope 3 Emissions
          <div style={{ fontWeight: 200, fontSize: "14px" }}>
            Indirect emissions occur due to an organization’s activities, but
            from sources it does not own or directly control.
          </div>
        </div>

        <div
          style={{
            margin: "18px 10px 18px 10px",
            display: "flex",
          }}
        >
          <select
            value={year1}
            onChange={(e) => {
              setYear1(e.target.value);
            }}
            style={{
              padding: "3px",
              borderRadius: "8px",
              width: "7.5rem",
              border: "1px solid grey",
              height: "30px",
              fontFamily: "lato",
            }}
          >
            {Object.keys(scopeData)?.map((i) => {
              return <option value={i}>{i}</option>;
            })}
          </select>
          <select
            value={year2}
            onChange={(e) => {
              setYear2(e.target.value);
            }}
            style={{
              padding: "3px",
              borderRadius: "8px",
              width: "7.5rem",
              border: "1px solid grey",
              height: "30px",
              fontFamily: "lato",
            }}
          >
            {Object.keys(scopeData)?.map((i) => {
              return <option value={i}>{i}</option>;
            })}
          </select>
          <div
            className="buttons"
            style={{
              background: "#F0F2F4",
              borderRadius: "3px",
              width: "4.5rem",
              marginLeft: "10px",
              height: "30px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              style={{
                background: `${!activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
                marginRight: "4px",
              }}
              onClick={() => {
                setActiveMode(false);
              }}
            >
              <i className="pi pi-table fs-19 " />
            </Button>
            <Button
              style={{
                background: `${activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
              }}
              onClick={() => {
                setActiveMode(true);
              }}
            >
              <i className="pi pi-chart-bar fs-19" />
            </Button>
          </div>
          <Button
            style={{
              padding: "6px",
              color: "white",
              height: "30px",
              marginLeft: "2px",
            }}
            onClick={() => {
              setActiveMode(true);
            }}
          >
            <i className="pi pi-download fs-19" />
          </Button>
        </div>
      </div>
      {activeMode && (
        <ResponsiveContainer width="100%" height={400}>
          <BarChart
            data={combinedData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <XAxis dataKey="month" />
            <YAxis
              label={{
                value: "MtCO2e",
                angle: -90,
                position: "insideLeft",
                offset: 0,
                style: { textAnchor: "middle", fill: "#555" },
              }}
            />
            <Tooltip />
            <Legend content={CustomScopeLegend} />
            <Bar
              dataKey={`${year1} category1`}
              stackId="a"
              fill="#C5DAE9"
              barSize={30}
              name="Category 1 - Purchase of Raw Materials and Services"
            ></Bar>
            <Bar
              dataKey={`${year1} category11`}
              stackId="a"
              fill="#ADD8E6"
              barSize={30}
              name="Category 11 - Use of Solid Products"
            ></Bar>
            <Bar
              dataKey={`${year1} category12`}
              stackId="a"
              fill="#003f5c"
              barSize={30}
              name="Category 12 - Final disposal of solid products"
            ></Bar>
            <Bar
              dataKey={`${year2} category1`}
              stackId="b"
              fill="#C5DAE9"
              barSize={30}
            ></Bar>
            <Bar
              dataKey={`${year2} category11`}
              stackId="b"
              fill="#ADD8E6"
              barSize={30}
            ></Bar>
            <Bar
              dataKey={`${year2} category12`}
              stackId="b"
              fill="#003f5c"
              barSize={30}
            ></Bar>
          </BarChart>
        </ResponsiveContainer>
      )}
      {!activeMode && (
        <div>
          <DataTable
            ref={dt}
            value={scopeData[year2]}
            tableClassName="font-lato"
          >
            <Column
              header="Month"
              style={{ minWidth: "8%" }}
              field="month"
              emptyMessage="No Assignment(s)"
            />
            <Column
              header="Consumption Emission"
              style={{ minWidth: "8%" }}
              field="category1"
              emptyMessage="No Assignment(s)"
            />
            <Column
              header="Refrigerant Emission"
              style={{ minWidth: "8%" }}
              field="category11"
              emptyMessage="No Assignment(s)"
            />
          </DataTable>
        </div>
      )}
    </>
  );
};

import React, { useEffect, useRef } from "react";
import * as d3 from "d3";

// Example data representing gender and age distribution across committees
const data = [
  {
    committee: "Audit Committee",
    male: { "20-30": 1, "31-40": 2, "41-50": 1, "51+": 0 },
    female: { "20-30": 2, "31-40": 1, "41-50": 1, "51+": 1 },
  },
  {
    committee: "Compensation Committee",
    male: { "20-30": 0, "31-40": 1, "41-50": 2, "51+": 0 },
    female: { "20-30": 1, "31-40": 1, "41-50": 2, "51+": 0 },
  },
  {
    committee: "Risk Committee",
    male: { "20-30": 2, "31-40": 2, "41-50": 0, "51+": 1 },
    female: { "20-30": 1, "31-40": 2, "41-50": 1, "51+": 0 },
  },
];

const CommitteeMemberDemographics = () => {
  const chartRef = useRef(null);

  useEffect(() => {
    renderStackedBarChart();
  }, []);

  const renderStackedBarChart = () => {
    const width = 800;
    const height = 400;
    const margin = { top: 30, right: 40, bottom: 40, left: 40 };

    // Clear any existing SVG before rendering the new chart
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Define the colors for the age and gender segments
    const colors = {
      "male-20-30": "#61c0bf",
      "male-31-40": "#6a4c93",
      "male-41-50": "#f06f6f",
      "male-51+": "#f1c40f",
      "female-20-30": "#3498db",
      "female-31-40": "#e74c3c",
      "female-41-50": "#2ecc71",
      "female-51+": "#9b59b6",
    };

    // Prepare the data by flattening it for stacking
    const flattenedData = data.flatMap((d) => {
      const totalMale = Object.keys(d.male).map((age) => ({
        committee: d.committee,
        gender: "Male",
        ageGroup: age,
        count: d.male[age],
      }));

      const totalFemale = Object.keys(d.female).map((age) => ({
        committee: d.committee,
        gender: "Female",
        ageGroup: age,
        count: d.female[age],
      }));

      return [...totalMale, ...totalFemale];
    });

    // Define the x and y scales
    const x0 = d3
      .scaleBand()
      .domain(data.map((d) => d.committee))
      .range([0, width - margin.left - margin.right])
      .padding(0.1);

    const x1 = d3
      .scaleBand()
      .domain(["male", "female"])
      .range([0, x0.bandwidth()])
      .padding(0.05);

    const y = d3
      .scaleLinear()
      .domain([0, d3.max(flattenedData, (d) => d.count)])
      .nice()
      .range([height - margin.top - margin.bottom, 0]);

    // Create the bars (stacked)
    const committeeGroups = svg
      .selectAll(".committee")
      .data(data)
      .enter()
      .append("g")
      .attr("class", "committee")
      .attr("transform", (d) => `translate(${x0(d.committee)}, 0)`);

    committeeGroups
      .selectAll(".bar")
      .data((d) =>
        ["male", "female"].flatMap((gender) =>
          Object.keys(d[gender]).map((age) => ({
            committee: d.committee,
            gender,
            ageGroup: age,
            count: d[gender][age],
          }))
        )
      )
      .enter()
      .append("rect")
      .attr("x", (d) => x1(d.gender))
      .attr("y", (d) => y(d.count))
      .attr("width", x1.bandwidth())
      .attr("height", (d) => height - margin.top - margin.bottom - y(d.count))
      .style("fill", (d) => colors[`${d.gender.toLowerCase()}-${d.ageGroup}`]);

    // Add x and y axis
    svg
      .append("g")
      .attr("class", "x-axis")
      .attr("transform", `translate(0,${height - margin.bottom})`)
      .call(d3.axisBottom(x0));

    svg.append("g").attr("class", "y-axis").call(d3.axisLeft(y));

    // Add labels for axes
    svg
      .selectAll(".x-axis text")
      .style("font-size", "12px")
      .style("fill", "#555");
    svg
      .selectAll(".y-axis text")
      .style("font-size", "12px")
      .style("fill", "#555");

    // Add legend
    const legend = svg
      .append("g")
      .attr("transform", `translate(${width - margin.right - 100}, 10)`);

    Object.keys(colors).forEach((key, i) => {
      const gender = key.split("-")[0];
      const age = key.split("-")[1];

      legend
        .append("rect")
        .attr("x", 0)
        .attr("y", i * 20)
        .attr("width", 18)
        .attr("height", 18)
        .style("fill", colors[key]);

      legend
        .append("text")
        .attr("x", 25)
        .attr("y", i * 20 + 9)
        .style("font-size", "12px")
        .style("fill", "#555")
        .attr("dy", ".35em")
        .text(`${gender.charAt(0).toUpperCase() + gender.slice(1)} ${age}`);
    });
  };

  return (
    <div>
      <h3 style={{ textAlign: "center", fontSize: "18px", fontWeight: "600" }}>
        Committee Member Demographics
      </h3>
      <div ref={chartRef}></div>
    </div>
  );
};

export default CommitteeMemberDemographics;

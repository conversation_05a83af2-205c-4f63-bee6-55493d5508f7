import React, { useState, useEffect } from "react";
import { Card } from "primereact/card";
import { Divider } from "primereact/divider";
import { Button } from "primereact/button";
import { Message } from "primereact/message";
import { InputText } from "primereact/inputtext";
import { Calendar } from "primereact/calendar";
import moment from "moment";
import Swal from "sweetalert2";
import APIServices from "../service/APIService";
import { API } from "../constants/api_url";

// Mapping arrays for Dealer Category and Dealer Zone
const zonalOfficeList = [
    { name: "Central", value: 1 },
    { name: "East", value: 2 },
    { name: "North", value: 3 },
    { name: "South", value: 9 },
    { name: "South1", value: 4 },
    { name: "South2", value: 5 },
    { name: "West", value: 8 },
    { name: "West1", value: 6 },
    { name: "West2", value: 7 }
];

const dealerType = [
    { name: 'Authorized Main Dealer', value: 1 },
    { name: 'Authorized Dealer', value: 2 },
    { name: 'Authorized Parts Stockist (APS)', value: 3 },
    { name: 'Area Office', value: 4 }
];

const criteriaColors = {
    environment: "#41AA95",
    social: "#FF9D7C",
    governance: "#5B8FF7",
    general: "#4F4F4F"
};

const getRatingName = (score) => {
    if (!score || score === '-' || score === 'NA') return 'NA';
    score = parseFloat(score);
    if (score >= 85) return 'Platinum';
    if (score > 70) return 'Gold';
    if (score > 55) return 'Silver';
    return 'Not Met';
};

const DealerReviewAction = ({ data, refresh, edit }) => {


    const [originalChecklist, setOriginalChecklist] = useState([]);
    const [modifiedChecklist, setModifiedChecklist] = useState([]);
    const [headersList, setHeadersList] = useState([]);
    const [error, setError] = useState("");

    // We track which question is in edit mode by indexes
    const [editIndex, setEditIndex] = useState({ headerIdx: null, criteriaIdx: null, qIdx: null });

    // We'll only allow editing actionToBeTaken & dueDate, but we'll display personResponsible
    const [editData, setEditData] = useState({
        actionToBeTaken: "",
        dueDate: null
    });

    useEffect(() => {
        if (data) {
            try {

              
                const parsedChecklist = JSON.parse(
                    data.dealerAuditorChecklistSubmission?.response || "[]"
                );

                setOriginalChecklist(parsedChecklist);

                // Clone the original for editing
                const cloned = JSON.parse(JSON.stringify(parsedChecklist));
                setModifiedChecklist(cloned);

                const grouped = buildHeadersAndCriteria(cloned);
                setHeadersList(grouped);
            } catch (e) {
                console.error(e);
                setOriginalChecklist([]);
                setModifiedChecklist([]);
                setHeadersList([]);
                setError("Invalid checklist format");
            }
        }
    }, [data]);

    const buildHeadersAndCriteria = (checklistArray) => {
        let currentHeaderLabel = null;
        const headersMap = {};

        for (const item of checklistArray) {
            if (item.type === "header") {
                currentHeaderLabel = item.label;
                if (!headersMap[currentHeaderLabel]) {
                    headersMap[currentHeaderLabel] = {
                        headerLabel: currentHeaderLabel,
                        groups: {}
                    };
                }
            } else if (item.type === "checklist-group") {
                if (!currentHeaderLabel) {
                    console.warn("Found checklist-group but no currentHeader:", item);
                    continue;
                }
                const criteriaKey = item.criteria || "general";
                if (!headersMap[currentHeaderLabel].groups[criteriaKey]) {
                    headersMap[currentHeaderLabel].groups[criteriaKey] = {
                        criteria: criteriaKey,
                        questions: []
                    };
                }
                headersMap[currentHeaderLabel].groups[criteriaKey].questions.push(
                    ...(item.questions || [])
                );
            }
        }

        const finalHeadersArray = Object.keys(headersMap).map((headerKey) => {
            const h = headersMap[headerKey];
            const groupArray = Object.keys(h.groups).map((critKey) => ({
                criteria: critKey,
                questions: h.groups[critKey].questions
            }));
            return {
                headerLabel: h.headerLabel,
                groups: groupArray
            };
        });

        return finalHeadersArray;
    };

    const renderSelectedOption = (options = []) => {
        const selected = options.find(opt => opt.checked === 1);
        return selected ? selected.label : "N/A";
    };

    // Edit button sets the fields we want to edit: actionToBeTaken, dueDate
    const handleEditClick = (question, headerIdx, criteriaIdx, qIdx) => {
        setEditIndex({ headerIdx, criteriaIdx, qIdx });
        setEditData({
            actionToBeTaken: question.actions?.actionToBeTaken || "",
            dueDate: question.actions?.dueDate ? new Date(question.actions.dueDate) : null
        });
    };

    const handleSaveClick = (headerIdx, criteriaIdx, qIdx) => {
        const updatedHeaders = [...headersList];
        const headerItem = updatedHeaders[headerIdx];
        if (!headerItem) return;

        const critGroup = headerItem.groups[criteriaIdx];
        if (!critGroup) return;

        const question = critGroup.questions[qIdx];
        if (!question || !question.actions) return;

        // Update only the two fields
        question.actions.actionToBeTaken = editData.actionToBeTaken;
        question.actions.dueDate = editData.dueDate
            ? editData.dueDate.toISOString()
            : null;

        setHeadersList(updatedHeaders);
        setEditIndex({ headerIdx: null, criteriaIdx: null, qIdx: null });
    };

    const handleCancelClick = () => {
        setEditIndex({ headerIdx: null, criteriaIdx: null, qIdx: null });
    };

    const getCriteriaColor = (criteria) => {
        return criteriaColors[criteria] || criteriaColors.general;
    };

    const calculateCriteriaScore = (questions) => {
        let totalScore = 0;
        questions.forEach(question => {
            if (question.options && Array.isArray(question.options)) {
                question.options.forEach(option => {
                    if (option.checked === 1 && option.score !== undefined) {
                        totalScore += option.score;
                    }
                });
            }
        });
        return totalScore;
    };

    // Approve: send the final "modifiedChecklist" (which has updates)
    const handleApprove = () => {
        const finalPayload = JSON.stringify(modifiedChecklist);
        console.log("APPROVE -> sending updated data:\n", finalPayload);
        // e.g. call your API

        APIServices.post(API.Review_Submission_Dealer(data.id), {
            response: finalPayload,
            formId: data.formId,
            vendorId: data.vendorId,
            dealerId: data.dealerId,
            created_by: data.created_by
        }).then((res) => {
            refresh()
        })
    };

    // Reject: revert changes to original
    const handleReject = () => {
        const revertedClone = JSON.parse(JSON.stringify(originalChecklist));
        setModifiedChecklist(revertedClone);

        const reGrouped = buildHeadersAndCriteria(revertedClone);
        setHeadersList(reGrouped);

        // Call API to update the type to 22
        APIServices.patch(API.DealerAuditorChecklistSubmission_Edit(data.dealerAuditorChecklistSubmission.id), {
            type: 22
        }).then((res) => {
            if (res.status === 204) {
                Swal.fire({
                    title: "Success!",
                    text: "Audit Returned",
                    icon: "success",
                    confirmButtonText: "OK"
                }).then(() => {
                    // Close the modal/dialog after success
                    refresh();
                });
            }
        }).catch((error) => {
            console.error("Error updating audit status:", error);
            Swal.fire({
                title: "Error!",
                text: "There was an issue returning the audit.",
                icon: "error",
                confirmButtonText: "OK"
            });
        });
    };

    const renderVendorMetadata = () => {
        console.log(data, ' Data')
        if (!data || !data.vendor) return null;

        const vendor = data.vendor;

        // Map dealer category value to name
        const getDealerCategoryName = (categoryValue) => {
            const category = dealerType.find(item => item.value === categoryValue);
            return category ? category.name : categoryValue;
        };

        // Map dealer zone value to name
        const getDealerZoneName = (zoneValue) => {
            const zone = zonalOfficeList.find(item => item.value === zoneValue);
            return zone ? zone.name : zoneValue;
        };
        const getMSIScore = (rowData) => {
            let score = null;
            try {
                const parsed = JSON.parse(rowData?.dealerAuditorChecklistSubmission?.score || '{"overallScore": null}');
                score = parsed?.overallScore;

            } catch (e) {
                score = null;
            }
            return score
        }
        const getMSIRating = (rowData) => {
            console.log(data)
            let score = null;
            try {
                const parsed = JSON.parse(rowData?.dealerAuditorChecklistSubmission?.score || '{"overallScore": null}');
                score = parsed?.overallScore;

            } catch (e) {
                score = null;
            }

            return score != null ? (
                score >= 85 ? (
                    <img
                        width={'100%'}
                        alt="Platinum Rating"
                        src={require('../assets/images/report/valuechain/platinum_rating.png').default}
                    />
                ) : score > 70 ? (
                    <img
                        width={'100%'}
                        alt="Gold Rating"
                        src={require('../assets/images/report/valuechain/gold_rating.png').default}
                    />
                ) : score > 55 ? (
                    <img
                        width={'100%'}
                        alt="Silver Rating"
                        src={require('../assets/images/report/valuechain/silver_rating.png').default}
                    />
                ) : (
                    'Not Met'
                )
            ) : (
                'NA'
            )

        }

        // Get MSI Grade and Rating from overall score
        let msiGrade = 'N/A';
        let msiRating = 'N/A';

        if (data.dealerAuditorChecklistSubmission?.score) {
            try {
                const scoreData = JSON.parse(data.dealerAuditorChecklistSubmission.score);
                if (scoreData.overallScore !== undefined) {
                    msiGrade = scoreData.overallScore;
                    msiRating = getRatingName(scoreData.overallScore);
                }
            } catch (e) {
                console.error('Error parsing score data:', e);
            }
        }

        return (
            <Card className="mb-3">
                <div className="vendor-metadata">
                    <h2>Dealer Information ({data.title})</h2>
                    <Divider />
                    <div className="grid">
                        <div className="col-12 md:col-6 lg:col-4">
                            <p><strong>Dealer Name:</strong> {vendor.dealerName || 'N/A'}</p>
                            <p><strong>Dealer Code:</strong> {vendor.code || 'N/A'}</p>
                            <p><strong>Dealer Category:</strong> {getDealerCategoryName(vendor.dealerCategory) || 'N/A'}</p>
                            <p><strong>Dealer Location:</strong> {vendor.dealerLocation || 'N/A'}</p>
                        </div>
                        <div className="col-12 md:col-6 lg:col-4">
                            <p><strong>Dealer Zone:</strong> {getDealerZoneName(vendor.dealerZone) || 'N/A'}</p>
                            <p><strong>Dealer Country:</strong> {vendor.dealerCountry || 'N/A'}</p>
                            <p><strong>Dealer AO:</strong> {vendor.dealerAO || 'N/A'}</p>
                            <p><strong>Created On:</strong> {vendor.created_on ? moment(vendor.created_on).format('DD-MM-YYYY') : 'N/A'}</p>
                        </div>
                        <div className="col-12 md:col-6 lg:col-4">
                            <p><strong>MSI Score:</strong> {getMSIScore(data) || 'N/A'}</p>
                            <p><strong>MSI Rating:</strong><div style={{ width: getMSIScore(data) > 55 ? 50 : 100, display: 'inline-table' }}>{getMSIRating(data) || 'N/A'}</div> </p>

                        </div>
                        <div className="col-12 md:col-6 lg:col-4">
                            <p><strong>Service Area Manager:</strong> {vendor.service?.areaManagerName || 'N/A'}</p>
                            <p><strong>Sales Area Manager:</strong> {vendor.sales?.areaManagerName || 'N/A'}</p>
                            {vendor.aps?.areaManagerName && <p><strong>APS Area Manager:</strong> {vendor.aps.areaManagerName}</p>}
                            {vendor.ao?.areaCommercialManagerName && <p><strong>AO Commercial Manager:</strong> {vendor.ao.areaCommercialManagerName}</p>}
                            <p><strong>MSI Grade:</strong> {msiGrade}</p>
                            <p><strong>MSI Rating:</strong> {msiRating}</p>
                        </div>
                    </div>
                </div>
            </Card>
        );
    };

    return (
        <div>
            {renderVendorMetadata()}
            <Card>
                {error && <Message severity="error" text={error} />}
                {headersList.length === 0 && <p>No data found.</p>}

                {headersList.map((headerItem, headerIdx) => (
                    <div key={headerIdx} style={{ marginBottom: "2rem" }}>
                        <h2 dangerouslySetInnerHTML={{ __html: headerItem.headerLabel }} />
                        <Divider />

                        {headerItem.groups.map((group, criteriaIdx) => {
                            const bgColor = getCriteriaColor(group.criteria);
                            console.log(group)
                            const criteriaScore = calculateCriteriaScore(group.questions);

                            return (
                                <div
                                    key={criteriaIdx}
                                    style={{
                                        marginBottom: "2rem",
                                    }}
                                >
                                    <h4 style={{ marginTop: 0, backgroundColor: bgColor, color: '#fff', padding: '7px 12px' }}>
                                        {group.criteria.toUpperCase()} ({criteriaScore})
                                    </h4>
                                    <Divider />

                                    <div style={{ paddingLeft: "1rem" }}>
                                        {group.questions.map((q, qIdx) => {
                                            if (!q.actions) return null;

                                            const isEditing =
                                                editIndex.headerIdx === headerIdx &&
                                                editIndex.criteriaIdx === criteriaIdx &&
                                                editIndex.qIdx === qIdx;
                                            console.log(q.actions, ' Actions')
                                            return (
                                                <div
                                                    key={qIdx}
                                                    style={{
                                                        marginBottom: "1rem",
                                                        border: "1px solid #ddd",
                                                        padding: "1rem",
                                                        borderRadius: "8px",
                                                        backgroundColor: "#fff"
                                                    }}
                                                >
                                                    <strong>{q.label}</strong>
                                                    <p>
                                                        <b>Selected Option:</b>{" "}
                                                        {renderSelectedOption(q.options)}
                                                    </p>
                                                    <p>
                                                        <b>Remarks:</b> {q.remarks || "N/A"}
                                                    </p>

                                                    {/* Show personResponsible as read-only (no editing) */}
                                                    <p>
                                                        <b>Person Responsible:</b>{" "}
                                                        {q.actions.personResponsible || "N/A"}
                                                    </p>

                                                    <div
                                                        style={{
                                                            backgroundColor: "#f9f9f9",
                                                            padding: "0.5rem",
                                                            borderRadius: "6px",
                                                            marginTop: "0.5rem"
                                                        }}
                                                    >
                                                        {isEditing ? (
                                                            <>
                                                                <div className="p-field">
                                                                    <label>
                                                                        <b>Action To Be Taken:</b>
                                                                    </label>
                                                                    <br />
                                                                    <InputText
                                                                        value={editData.actionToBeTaken}
                                                                        onChange={(e) =>
                                                                            setEditData((prev) => ({
                                                                                ...prev,
                                                                                actionToBeTaken: e.target.value
                                                                            }))
                                                                        }
                                                                    />
                                                                </div>

                                                                <div className="p-field mt-2">
                                                                    <label>
                                                                        <b>Due Date:</b>
                                                                    </label>
                                                                    <br />
                                                                    <Calendar
                                                                        value={editData.dueDate}
                                                                        onChange={(e) =>
                                                                            setEditData((prev) => ({
                                                                                ...prev,
                                                                                dueDate: e.value
                                                                            }))
                                                                        }
                                                                        dateFormat="dd-mm-yy"
                                                                    />
                                                                </div>

                                                                <div className="p-mt-2">
                                                                    <Button
                                                                        label="Save"
                                                                        icon="pi pi-check"
                                                                        className="p-mr-2"
                                                                        onClick={() =>
                                                                            handleSaveClick(headerIdx, criteriaIdx, qIdx)
                                                                        }
                                                                    />
                                                                    <Button
                                                                        label="Cancel"
                                                                        icon="pi pi-times"
                                                                        severity="danger"
                                                                        onClick={handleCancelClick}
                                                                    />
                                                                </div>
                                                            </>
                                                        ) : (
                                                            <>
                                                                <p>
                                                                    <b>Action To Be Taken:</b>{" "}
                                                                    {q.actions.actionToBeTaken || "N/A"}
                                                                </p>
                                                                <p>
                                                                    <b>Due Date:</b>{" "}
                                                                    {q.actions.dueDate
                                                                        ? moment(q.actions.dueDate).format("DD-MM-YYYY")
                                                                        : "N/A"}
                                                                </p>

                                                                {/* Display Evidence Attachments */}
                                                                {q.actions.evidence && Array.isArray(q.actions.evidence) && q.actions.evidence.length > 0 && (
                                                                    <div style={{ marginTop: "0.5rem" }}>
                                                                        <p><b>Evidence Attachments:</b></p>
                                                                        <div className="row">
                                                                            {q.actions.evidence.map((evidence, evidenceIdx) => {
                                                                                if (!evidence.value) return null;

                                                                                const fileExt = evidence.value.split('.').pop().toLowerCase();

                                                                                if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
                                                                                    return (
                                                                                        <div className="col-md-3 mb-2" key={evidenceIdx}>
                                                                                            <a href={API.Docs + evidence.value} target="_blank" rel="noopener noreferrer">
                                                                                                <img
                                                                                                    src={API.Docs + evidence.value}
                                                                                                    alt={`evidence-${evidenceIdx}`}
                                                                                                    className="img-fluid rounded shadow-sm"
                                                                                                    style={{ maxHeight: '100px', objectFit: 'cover' }}
                                                                                                />
                                                                                            </a>
                                                                                        </div>
                                                                                    );
                                                                                } else if (fileExt === 'pdf') {
                                                                                    return (
                                                                                        <div className="col-md-3 mb-2" key={evidenceIdx}>
                                                                                            <a href={API.Docs + evidence.value} target="_blank" rel="noopener noreferrer">
                                                                                                📄 View PDF
                                                                                            </a>
                                                                                        </div>
                                                                                    );
                                                                                } else {
                                                                                    return (
                                                                                        <div className="col-md-3 mb-2" key={evidenceIdx}>
                                                                                            <a href={API.Docs + evidence.value} target="_blank" rel="noopener noreferrer">
                                                                                                {evidence.filename || evidence.value}
                                                                                            </a>
                                                                                        </div>
                                                                                    );
                                                                                }
                                                                            })}
                                                                        </div>
                                                                    </div>
                                                                )}

                                                                {edit === 1 && <Button
                                                                    label="Edit"
                                                                    icon="pi pi-pencil"
                                                                    className="p-button-text p-mt-2"
                                                                    onClick={() =>
                                                                        handleEditClick(q, headerIdx, criteriaIdx, qIdx)
                                                                    }
                                                                />
                                                                }
                                                            </>
                                                        )}
                                                    </div >
                                                </div >
                                            );
                                        })}
                                    </div >
                                </div >
                            );
                        })}
                    </div >
                ))}
            </Card >

            {edit === 1 && <div style={{ textAlign: "right", marginTop: "2rem" }}>
                <Button
                    label="Save and Assign Action"
                    icon="pi pi-check"
                    className="p-mr-2"
                    onClick={handleApprove}
                />
                <Button
                    label="Reject"
                    icon="pi pi-times"
                    severity="danger"
                    onClick={handleReject}
                />
            </div>}
        </div >
    );
};

export default DealerReviewAction;

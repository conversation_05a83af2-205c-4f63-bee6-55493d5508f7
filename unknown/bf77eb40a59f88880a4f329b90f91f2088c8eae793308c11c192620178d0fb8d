import React, { useRef, useState, useEffect } from "react";
import { But<PERSON> } from "primereact/button";
import { Column } from "primereact/column";
import { DataTable } from "primereact/datatable";
import { Menu } from "primereact/menu";
import ExcelJS from "exceljs";
import { saveAs } from "file-saver";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import { Checkbox } from "@material-ui/core";
import { useSelector } from "react-redux";
import APIServices from "../../service/APIService";
import { API } from "../../constants/api_url"; 
import {
  BarChart,
  Bar,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  LineChart,
  ResponsiveContainer,
} from "recharts";

// const initialData = [
//   { year: "2019", hazardousWaste: 51455, nonHazardousWaste: 243134 },
//   { year: "2020", hazardousWaste: 34449, nonHazardousWaste: 84578 },
//   { year: "2021", hazardousWaste: 61378, nonHazardousWaste: 141645 },
//   { year: "2022", hazardousWaste: 60781, nonHazardousWaste: 92104 },
//   { year: "2023", hazardousWaste: 43059, nonHazardousWaste: 119745 },
// ];

const OverviewWaste = () => {
  const [initialData, setInitialData] = useState([]);
  const [data, setData] = useState(initialData);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [activeMode, setActiveMode] = useState(true);
  const menuRef = useRef(null);
  const tableRef = useRef(null);
  const chartRef = useRef(null);
  const admin_data = useSelector((state) => state.user.admindetail);
  const [visibleSeries, setVisibleSeries] = useState({
    hazardous: true,
    nonHazardous: true,
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await APIServices.get(API.QN_Submit_UP(admin_data.id));
        const data = response?.data;

        const transformedRawData = data.flatMap((item, index) =>
          item.response.map((res, resIndex) => ({
            sno: `${index + 1}.${resIndex + 1}`, // Create unique serial number
            title: res.label || "No Title",
            value: res.type === "number" ? res.value || 0 : "N/A",
            periodFrom: item.reporting_period?.[0] || "N/A",
            periodTo: "N/A", // Replace with actual period end if available
            entity: `Entity ${item.locationId}`,
            reporter: `User ${item.submitted_by}`,
            reportedDate: item.submitted_on,
            reporterComments: item.logs?.[0]?.remarks || "No Comments",
            reviewer: `User ${item.reviewed_by}`,
            reviewedDate: item.reviewed_on,
            reviewerComments:
              item.return_remarks?.[0]?.remarks || "No Comments",
            approver: item.approved_by ? `User ${item.approved_by}` : "N/A",
            dateOfApproval: item.approved_on || "N/A",
            approverComments: item.approver_modified_on
              ? "Modified by Approver"
              : "No Comments",
          }))
        );

        // Map response data for metricsData (example transformation)
        const transformedMetricsData = data.map((item) => ({
          reportingFrequencies: "Annual", // Replace with actual data if available
          contributingEntities: `Entity ${item.locationId}`,
          contributingDataPoints: item.response
            .filter((res) => res.type === "number")
            .map((res) => res.label)
            .join(", "),
          totalQuantity: item.response
            .filter((res) => res.type === "number")
            .reduce((sum, res) => sum + (res.value || 0), 0),
          unitOfMeasure: "Units", // Replace with actual unit if available
          emissionFactorName: "N/A", // Replace with actual data if available
          emissionFactorValue: "N/A", // Replace with actual data if available
          formula: "Sum of Values", // Replace with actual formula
          computedValue: "N/A", // Replace with actual computed value if available
          entitySummary: `Entity ${item.locationId} reported ${item.response.length} data points.`,
        }));
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, [admin_data.id]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleCheckboxChange = (key) => {
    setVisibleSeries((prevState) => ({
      ...prevState,
      [key]: !prevState[key],
    }));
    setData((prevData) =>
      prevData.map((item) => ({
        ...item,
        [key]: visibleSeries[key]
          ? 0
          : initialData.find((d) => d.year === item.year)[key],
      }))
    );
  };

  const CustomWasteLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
        }}
      >
        {payload.map((entry, index) => (
          <li key={`item-${index}`} style={{ color: entry.color }}>
            <Checkbox
              checked={visibleSeries[entry.dataKey]}
              onChange={() => handleCheckboxChange(entry.dataKey)}
              style={{
                color: entry.color,
                marginRight: 4,
                fontSize: "20px",
              }}
            />

            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  const panelItems = [
    {
      items: [
        {
          label: "Export as Excel",
          icon: "pi pi-file-excel",
          command: () => {
            downloadExcelWithImage(chartRef);
          },
        },
        {
          label: "Export as PDF",
          icon: "pi pi-file-pdf",
          command: () => {
            downloadPdfWithImage(chartRef);
          },
        },
        {
          label: "Export as JPG",
          icon: "pi pi-image",
          command: () => {
            downloadChartAsJpg(chartRef);
          },
        },
        activeMode && {
          label: "Print",
          icon: "pi pi-print",
          command: () => {
            printChart(chartRef);
          },
        },
      ],
    },
  ];

  const downloadExcelWithImage = async (chartRef) => {
    // Create a new workbook and add a worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Waste Management");

    // Add title in A1 and merge across columns
    worksheet.mergeCells("A1:E1");
    worksheet.getCell("A1").value =
      "Waste Management Report, Filtered location";
    worksheet.getCell("A1").font = { bold: true, size: 16 };
    worksheet.getCell("A1").alignment = {
      vertical: "middle",
      horizontal: "center",
    };

    // Add timestamp in A2 and merge across columns
    const timestamp = new Date().toLocaleString();
    worksheet.mergeCells("A2:E2");
    worksheet.getCell("A2").value = `Generated on: ${timestamp}`;
    worksheet.getCell("A2").font = { italic: true, size: 12 };
    worksheet.getCell("A2").alignment = {
      vertical: "middle",
      horizontal: "center",
    };

    // Leave an empty row for spacing (Row 3)
    worksheet.getRow(3).values = [];

    // Manually set the column headers in row 4
    worksheet.getRow(4).values = [
      "Year",
      visibleSeries.hazardous && "Hazardous waste",
      visibleSeries.nonHazardous && "Non-Hazardous Waste",
    ];

    // Define columns for the table (this does NOT affect Row 4 headers)
    worksheet.columns = [
      { key: "year", width: 10 },
      visibleSeries.hazardous && { key: "hazardous", width: 15 },
      visibleSeries.nonHazardous && {
        key: "nonHazardous",
        width: 15,
      },
    ];

    // Populate data rows starting from row 5
    initialData.forEach((row) => worksheet.addRow(row));

    // Capture the chart as an image
    //chartRef.current.data.dataset[0].data = initialData;
    const canvas = await html2canvas(chartRef.current);

    const imageData = canvas.toDataURL("image/png");

    // Add the image to the workbook below the data
    const imageId = workbook.addImage({
      base64: imageData,
      extension: "png",
    });

    worksheet.addImage(imageId, {
      tl: { col: 0.5, row: data.length + 6 }, // Adjust row based on data length
      ext: { width: 500, height: 300 },
    });

    // Download the Excel file
    const buffer = await workbook.xlsx.writeBuffer();
    saveAs(new Blob([buffer]), "Waste_Management.xlsx");
  };

  const downloadPdfWithImage = async (chartRef) => {
    // Create a new jsPDF instance
    const doc = new jsPDF();

    // Add title
    doc.setFontSize(16);
    doc.setFont("helvetica", "bold");
    doc.text("Waste Management Report", 20, 20); // Adjust position (x, y)

    // Add timestamp
    const timestamp = new Date().toLocaleString();
    doc.setFontSize(12);
    doc.setFont("helvetica", "italic");
    doc.text(`Generated on: ${timestamp}`, 20, 30); // Adjust position (x, y)

    // Add a line break
    doc.text("\n", 20, 40);

    // Define column headers
    const headers = [
      "Year",
      visibleSeries.hazardous && "Hazardous waste",
      visibleSeries.nonHazardous && "Non-Hazardous waste",
    ];
    const dataRows = initialData.map((row) => [
      row.year,
      row.hazardous,
      row.nonHazardous,
    ]);

    // Add table (with headers and data rows)
    doc.autoTable({
      head: [headers],
      body: dataRows,
      startY: 50, // Start after the title and timestamp
      theme: "grid",
      headStyles: { fillColor: [0, 0, 0] }, // Dark background for header
      styles: { cellPadding: 2, fontSize: 10 },
    });

    // Capture the chart as an image
    const canvas = await html2canvas(chartRef.current);
    const imageData = canvas.toDataURL("image/png");

    // Add the chart image below the table
    doc.addImage(imageData, "PNG", 20, doc.lastAutoTable.finalY + 10, 180, 100); // Adjust position and size

    // Download the PDF file
    doc.save("Waste_Management.pdf");
  };

  const downloadChartAsJpg = async (chartRef) => {
    // Capture the chart as an image using html2canvas
    const canvas = await html2canvas(chartRef.current);

    // Convert the canvas to JPG data
    const imageData = canvas.toDataURL("image/jpeg");

    // Create a link element to trigger the download
    const link = document.createElement("a");
    link.href = imageData;
    link.download = "Waste_Management.jpg"; // Filename for the image
    link.click(); // Programmatically click the link to trigger the download
  };

  const printChart = async (chartRef) => {
    // Capture the chart as an image using html2canvas
    const canvas = await html2canvas(chartRef.current);
    const chartImage = canvas.toDataURL("image/png");

    // Create a new window for printing
    const printWindow = window.open("", "", "height=600,width=800");

    // Add some basic styles for the printed content
    const styles = `
      <style>
        body { font-family: Arial, sans-serif; }
        h1 { text-align: center; }
        h2 { margin-top: 20px; }
        img { max-width: 100%; height: auto; margin-bottom: 20px; }
        .table-container { margin-top: 20px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 10px; text-align: left; border: 1px solid #ddd; }
      </style>
    `;

    // Set the content of the print window
    printWindow.document.write(
      "<html><head><title>Print Report</title>" + styles + "</head><body>"
    );
    printWindow.document.write("<h1>Waste Management Report</h1>");

    // Add chart image
    printWindow.document.write("<h2>Chart</h2>");
    printWindow.document.write(`<img src="${chartImage}" />`);

    // Add table content

    // Check if table content is available and print it

    printWindow.document.write("</body></html>");

    // Close the document and trigger the print dialog
    printWindow.document.close();
    printWindow.print();
  };

  return (
    <>
      <div
        style={{
          width: "100%",
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        <div
          style={{
            fontFamily: "Lato",
            fontSize: "16px",
            fontWeight: 700,
            lineHeight: "19.2px",
            textAlign: "left",
            margin: "18px 10px 18px 10px",
          }}
        >
          Hazardous Waste Generated
          <div style={{ fontWeight: 200, fontSize: "14px" }}>
            Quantity of Hazardous waste generated across the organization
          </div>
        </div>
        <div
          style={{
            margin: "18px 10px 18px 10px",
            display: "flex",
          }}
        >
          {/* <input
            type="month"
            style={{
              padding: "3px",
              borderRadius: "8px",
              width: "15rem",
              border: "1px solid grey",
              height: "30px",
              fontFamily: "lato",
            }}
          /> */}
          <div
            className="buttons"
            style={{
              background: "#F0F2F4",
              borderRadius: "3px",
              width: "4.5rem",
              marginLeft: "10px",
              height: "30px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              style={{
                background: `${!activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
                marginRight: "4px",
              }}
              onClick={() => {
                setActiveMode(false);
              }}
            >
              <i className="pi pi-table fs-19 " />
            </Button>
            <Button
              style={{
                background: `${activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
              }}
              onClick={() => {
                setActiveMode(true);
              }}
            >
              <i className="pi pi-chart-bar fs-19" />
            </Button>
          </div>
          <div ref={menuRef}>
            <Button
              style={{
                color: "black",
                height: "30px",
                marginLeft: "3px",
                background: "#F0F2F4",
                border: "0px",
                padding: "6px",
                position: "relative",
              }}
              onClick={() => {
                setDropdownOpen(!dropdownOpen);
              }}
            >
              <i className="pi pi-angle-down fs-19" />
            </Button>
            {dropdownOpen && (
              <Menu
                model={panelItems}
                style={{
                  position: "absolute",
                  right: 45,
                  zIndex: "1",
                  padding: 0,
                }}
              ></Menu>
            )}
          </div>
        </div>
      </div>
      {activeMode && (
        <div style={{ width: "100%", height: 400 }} ref={chartRef}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              barSize={60}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis
                label={{
                  value: "Tons",
                  angle: -90,
                  position: "insideLeft",
                }}
                tick={{
                  fontSize: 10, // Adjust the font size as needed
                }}
              />
              <Tooltip />
              <Legend content={CustomWasteLegend} />

              {/* Bars for Hazardous and Non-Hazardous Waste */}
              <Bar dataKey="hazardous" fill="#003f5c" name="Hazardous waste" />
              <Bar
                dataKey="nonHazardous"
                fill="#ffa600"
                name="Non-hazardous waste"
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      )}
      {!activeMode && (
        <div>
          <DataTable ref={tableRef} value={data} tableClassName="font-lato">
            <Column
              header="Year"
              style={{ minWidth: "8%" }}
              field="year"
              emptyMessage="No Assignment(s)"
            />
            {visibleSeries.hazardous && (
              <Column
                header="Hazardous Waste"
                style={{ minWidth: "8%" }}
                field="hazardous"
                emptyMessage="No Assignment(s)"
              />
            )}
            {visibleSeries.nonHazardous && (
              <Column
                header="Non Hazardous Waste"
                style={{ minWidth: "8%" }}
                field="nonHazardous"
                emptyMessage="No Assignment(s)"
              />
            )}
          </DataTable>
        </div>
      )}
    </>
  );
};

export default OverviewWaste;

import React, { useEffect, useState } from "react";
import APIServices from "../service/APIService";
import { API } from "../constants/api_url";
import { DateTime } from "luxon";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { TabView, TabPanel } from 'primereact/tabview';
import { MultiSelect } from "primereact/multiselect";
import { Tag } from "primereact/tag";
import { InputText } from "primereact/inputtext";
import { Dialog } from "primereact/dialog";
import { useSelector } from "react-redux";
import moment from 'moment';
// import DealerSubmissionView from "../client/admin/TVS/MSI/DealerSubmissionView";
// import DealerCompletedReport from "../client/admin/TVS/MSI/DealerCompletedReport";
import { getDate } from "../components/BGHF/helper";
import "./MSISubmissionsTab.css";

const MSISubmissionsTab = () => {
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const [dealerData, setDealerData] = useState([]);
    const [dealerSelfSubmissions, setDealerSelfSubmissions] = useState([]);
    const [globalFilter, setGlobalFilter] = useState('');
    const [submissionDialog, setSubmissionDialog] = useState(false);
    const [submissionData, setSubmissionData] = useState(null);
    const [selectedAudit, setSelectedAudit] = useState(null);
    const [reportDialog, setReportDialog] = useState(false);
    const [loading, setLoading] = useState(true);
    const [dateFilter, setDateFilter] = useState({ start: null, end: null });

    // For filtering
    const [selfAssessmentFilters, setSelfAssessmentFilters] = useState({
        dealerName: { matchMode: 'in', value: null },
        location: { matchMode: 'in', value: null },
        msiId: { matchMode: 'in', value: null },
        selfAssessmentGrade: { matchMode: 'in', value: null },
        zone: { matchMode: 'in', value: null },
        cat: { matchMode: 'in', value: null }
    });

    const [calibrationFilters, setCalibrationFilters] = useState({
        dealerName: { matchMode: 'in', value: null },
        location: { matchMode: 'in', value: null },
        msiId: { matchMode: 'in', value: null },
        grade: { matchMode: 'in', value: null },
        zone: { matchMode: 'in', value: null },
        cat: { matchMode: 'in', value: null },
        calibratorName: { matchMode: 'in', value: null },
        latestSubmission: { matchMode: 'in', value: null }
    });

    const admin_data = useSelector((state) => state.user.admindetail);
    const userList = useSelector(state => state.userlist.userList);
    const dealerList = useSelector(state => state.userlist.dealerList);
    const tvsExtUserList = useSelector(state => state.userlist.tvsExtUserList);
    const dealerType = [{ name: 'Authorized Main Dealer', value: 1 }, { name: 'Authorized Dealer', value: 2 }, { name: 'Authorized Parts Stockist (APS)', value: 3 }, { name: 'Area Office', value: 4 }];
    const zonalOfficeList = [{ name: "Central", value: 1 }, { name: "East", value: 2 }, { name: "North", value: 3 }, { name: "South", value: 9 }, { name: "South1", value: 4 }, { name: "South2", value: 5 }, { name: "West", value: 8 }, { name: "West1", value: 6 }, { name: "West2", value: 7 }, { name: "TN", value: 10 }, { name: "North1", value: 11 }, { name: "North2", value: 12 }];

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setLoading(true);
        try {
            // Define the dealer assessment URI with includes
            let dealerAssUri = {
                "include": ['dealer', 'dealerAuditorChecklistSubmission', 'vendor', 'actions']
            };

            // Fetch dealer data
            const dealerResponse = await APIServices.get(API.DealerAssessmentAss_Up(admin_data.id) + `?filter=${encodeURIComponent(JSON.stringify(dealerAssUri))}`);

            // Process dealer data
            const processedDealerData = dealerResponse.data.map(item => {
                return {
                    ...item,
                    cat: dealerType.find(i => i.value === item?.vendor?.dealerCategory)?.name || 'Not Found',
                    msiId: getCalibirationId(item),
                    dealerName: item?.vendor?.dealerName || 'NA',
                    location: item?.vendor?.dealerLocation || 'NA',
                    zone: zonalOfficeList.find(i => i.value === item?.vendor?.dealerZone)?.name || 'Not Found'
                };
            });

            setDealerData(processedDealerData);

            // Fetch dealer self-submissions
            const selfSubmissionsResponse = await APIServices.get(API.DealerSelfSubmission);
            setDealerSelfSubmissions(selfSubmissionsResponse?.data || []);

            setLoading(false);
        } catch (error) {
            console.error('Error fetching data:', error);
            setLoading(false);
        }
    };

    const getCalibirationId = (rowData) => {
        return 'MSI-' + (rowData?.vendor?.code || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy');
    };

    // Filter templates
    const RowFilterTemplate = (options, field) => {
        const items = [...new Set(dealerData.map(item => {
            if (field === 'dealerName') return item.vendor?.dealerName;
            if (field === 'location') return item.vendor?.dealerLocation;
            if (field === 'msiId') return getCalibirationId(item);
            if (field === 'zone') return zonalOfficeList.find(x => x.value === item?.vendor?.dealerZone)?.name;
            if (field === 'cat') return dealerType.find(x => x.value === item.vendor?.dealerCategory)?.name;
            if (field === 'selfAssessmentGrade') return getRatingName(getSelfAssessmentScore(item));
            if (field === 'grade') return getRatingName(getCalibrationScore(item));
            if (field === 'calibratorName') return getCalibratorName(item);
            if (field === 'latestSubmission') return getLastSubmissionMonth(item);
            return '';
        }).filter(Boolean))];

        return (
            <MultiSelect
                value={options.value}
                options={items.map(item => ({ label: item, value: item }))}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Select"
                className="p-column-filter"
            />
        );
    };

    // Rating calculation
   const getRatingName = (score) => {
        if (!score || score === '-' || score === 'NA') return 'NA';
        score = parseFloat(score);
        if (score >= 85) return 'Platinum';
        if (score > 70) return 'Gold';
        if (score > 55) return 'Silver';
        return 'Not Met';
    };

    // Self-assessment score calculation
    const getSelfAssessmentScore = (rowData) => {
        if (!Array.isArray(dealerSelfSubmissions)) return 'NA';

        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                return dateB - dateA;
            });

        if (matched.length === 0) return 'NA';

        try {
            const scoreObj = JSON.parse(matched[0].score || '{}');
            return scoreObj.overallScore || 'NA';
        } catch (e) {
            return 'NA';
        }
    };

    // Calibration score calculation
    const getCalibrationScore = (rowData) => {
        return JSON.parse(rowData?.dealerAuditorChecklistSubmission?.score || '{overallScore:"-"}')?.overallScore;
    };

    // Get last submission month
    const getLastSubmissionMonth = (rowData) => {
        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                return dateB - dateA;
            });

        if (matched.length === 0 || !matched[0]?.reporting_period?.[0]) return '';
        return DateTime.fromFormat(matched[0].reporting_period[0], 'LL-yyyy').toFormat('LLLL yyyy');
    };

    // Get calibrator name
    const getCalibratorName = (rowData) => {
        if (rowData?.dealerAuditorChecklistSubmission) {
            let findId = rowData?.dealerAuditorChecklistSubmission?.modified_by || rowData?.dealerAuditorChecklistSubmission?.created_by || null;
            return userList.find(i => i.id === findId)?.information?.empname || '';
        } else {
            return 'Not Assigned';
        }
    };

    // Templates for DataTable columns
    const nameTemplate = (rowData) => {
        return rowData.vendor?.dealerName || 'NA';
    };

    const locationTemplate = (rowData) => {
        return rowData.vendor?.dealerLocation || 'NA';
    };

    const zoneTemplate = (rowData) => {
        return zonalOfficeList.find(x => x.value === rowData?.vendor?.dealerZone)?.name || 'NA';
    };

    const categoryTemplate = (rowData) => {
        return dealerType.find(x => x.value === rowData.vendor?.dealerCategory)?.name || 'NA';
    };

    const ratingTemplate = (rowData) => {
        const rating = activeTabIndex === 0
            ? getRatingName(getSelfAssessmentScore(rowData))
            : getRatingName(getCalibrationScore(rowData));

        let className = '';
        if (rating === 'Platinum') className = 'status-tag-platinum';
        else if (rating === 'Gold') className = 'status-tag-gold';
        else if (rating === 'Silver') className = 'status-tag-silver';
        else if (rating === 'Bronze') className = 'status-tag-bronze';
        else if (rating === 'Needs Improvement') className = 'status-tag-needs-improvement';
        else className = 'status-tag-gray';

        return <Tag className={className}>{rating}</Tag>;
    };

    const calibrationIdBodyTemplate = (rowData) => {
        return (
            <a href="#" onClick={(e) => {
                e.preventDefault();
                setSelectedAudit(rowData);
                setReportDialog(true);
            }}>
                {getCalibirationId(rowData)}
            </a>
        );
    };

    const viewSubmissionTemplate = (rowData) => {
        if (activeTabIndex === 0) {
            // Self-assessment view button
            const matched = dealerSelfSubmissions
                .filter(sub => sub.dealerId === rowData.dealerId)
                .sort((a, b) => {
                    const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                    const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                    return dateB - dateA;
                });

            if (matched.length === 0) return <button className="btn btn-sm btn-secondary" disabled>View</button>;

            return (
                <button
                    className="btn btn-sm btn-primary"
                    onClick={async () => {
                        try {
                            const response = await APIServices.get(API.DealerSelfSubmissionData + `/${matched[0].id}`);
                            const parsed = JSON.parse(response.data.data);

                            // Prepare dealer info for display
                            const dealerInfo = {
                                dealerName: rowData?.vendor?.dealerName || 'NA',
                                location: rowData?.vendor?.dealerLocation || 'NA',
                                zone: zonalOfficeList.find(x => x.value === rowData?.vendor?.dealerZone)?.name || 'NA',
                                category: dealerType.find(x => x.value === rowData.vendor?.dealerCategory)?.name || 'NA',
                                selfAssessmentMonth: getLastSubmissionMonth(rowData),
                                selfAssessmentScore: getSelfAssessmentScore(rowData),
                                msiRating: getRatingName(getSelfAssessmentScore(rowData))
                            };

                            setSubmissionData(parsed);
                            setSelectedAudit(dealerInfo);
                            setSubmissionDialog(true);
                        } catch (err) {
                            console.error('Invalid JSON in response', err);
                            alert('Invalid submission data');
                        }
                    }}
                >
                    View
                </button>
            );
        } else {
            // MSI Calibration view button
            return (
                <button
                    className="btn btn-sm btn-primary"
                    onClick={async () => {
                        try {
                            const response = await APIServices.get(API.DealerAuditorChecklistSubmissionData + `/${rowData.dealerAuditorChecklistSubmission.id}`);
                            const parsed = JSON.parse(response.data.data);

                            // Prepare dealer info for display
                            const dealerInfo = {
                                calibrationId: getCalibirationId(rowData),
                                dealerName: rowData?.vendor?.dealerName || 'NA',
                                location: rowData?.vendor?.dealerLocation || 'NA',
                                zone: zonalOfficeList.find(x => x.value === rowData?.vendor?.dealerZone)?.name || 'NA',
                                category: dealerType.find(x => x.value === rowData.vendor?.dealerCategory)?.name || 'NA',
                                selfAssessmentMonth: getLastSubmissionMonth(rowData),
                                selfAssessmentScore: getSelfAssessmentScore(rowData),
                                calibrationScore: getCalibrationScore(rowData),
                                msiRating: getRatingName(getCalibrationScore(rowData))
                            };

                            setSubmissionData(parsed);
                            setSelectedAudit(dealerInfo);
                            setSubmissionDialog(true);
                        } catch (err) {
                            console.error('Invalid JSON in response', err);
                            alert('Invalid submission data');
                        }
                    }}
                >
                    View
                </button>
            );
        }
    };

    return (
        <div className="p-4">
            <h1>MSI Submissions</h1>

            <div className="mb-2 text-end">
                <span className="p-input-icon-left">
                    <i className="pi pi-search" />
                    <InputText
                        type="search"
                        placeholder="Global Search"
                        value={globalFilter}
                        onChange={(e) => setGlobalFilter(e.target.value)}
                    />
                </span>
            </div>

            <TabView
                activeIndex={activeTabIndex}
                onTabChange={(e) => setActiveTabIndex(e.index)}
            >
                <TabPanel header="Submitted Self Assessment">
                    <DataTable
                        value={dealerData.filter(dealer => {
                            // Filter dealers with valid self-assessment scores
                            const matched = dealerSelfSubmissions
                                .filter(sub => sub.dealerId === dealer.dealerId)
                                .sort((a, b) => {
                                    const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                                    const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                                    return dateB - dateA;
                                });

                            if (matched.length > 0) {
                                try {
                                    const scoreObj = JSON.parse(matched[0].score || '{}');
                                    const score = scoreObj.overallScore;
                                    return score !== undefined && score !== null && score !== 'NA' && score !== '-';
                                } catch (e) {
                                    return false;
                                }
                            }
                            return false;
                        }).map((x, index) => ({ ...x, tableIndex: index + 1 }))}
                        paginator
                        rows={10}
                        scrollable
                        scrollHeight="500px"
                        filters={selfAssessmentFilters}
                        globalFilter={globalFilter}
                        className="mt-2 h-500"
                        loading={loading}
                    >
                        <Column sortable field="tableIndex" header="S.No" />
                        <Column sortable field="msiId" header="Calibration ID" body={calibrationIdBodyTemplate} showFilterMatchModes={false}
                            filter filterElement={(options) => RowFilterTemplate(options, "msiId")} />
                        <Column sortable field="dealerName" header="Name" body={nameTemplate} showFilterMatchModes={false}
                            filter filterElement={(options) => RowFilterTemplate(options, "dealerName")} />
                        <Column sortable field="location" header="Location" body={locationTemplate} showFilterMatchModes={false}
                            filter filterElement={(options) => RowFilterTemplate(options, "location")} />
                        <Column sortable field="zone" header="Zone" body={zoneTemplate} showFilterMatchModes={false}
                            filter filterElement={(options) => RowFilterTemplate(options, "zone")} />
                        <Column sortable field="cat" header="Category" body={categoryTemplate} showFilterMatchModes={false}
                            filter filterElement={(options) => RowFilterTemplate(options, "cat")} />
                        <Column field="selfAssessmentGrade" filter showFilterMatchModes={false}
                            filterElement={(options) => RowFilterTemplate(options, "selfAssessmentGrade")}
                            header="MSI Rating" body={ratingTemplate} />
                        <Column header="View Submission" body={viewSubmissionTemplate} />
                    </DataTable>
                </TabPanel>

                <TabPanel header="Submitted MSI Reports">
                    <DataTable
                        value={dealerData.filter(x => [1, 2].includes(x.dealerAuditorChecklistSubmission?.type))
                            .map((x, index) => ({ ...x, tableIndex: index + 1 }))}
                        paginator
                        rows={10}
                        scrollable
                        scrollHeight="500px"
                        filters={calibrationFilters}
                        globalFilter={globalFilter}
                        className="mt-2 h-500"
                        loading={loading}
                    >
                        <Column sortable field="tableIndex" header="S.No" />
                        <Column sortable field="msiId" header="Calibration ID" body={calibrationIdBodyTemplate} showFilterMatchModes={false}
                            filter filterElement={(options) => RowFilterTemplate(options, "msiId")} />
                        <Column sortable field="dealerName" header="Name" body={nameTemplate} showFilterMatchModes={false}
                            filter filterElement={(options) => RowFilterTemplate(options, "dealerName")} />
                        <Column sortable field="location" header="Location" body={locationTemplate} showFilterMatchModes={false}
                            filter filterElement={(options) => RowFilterTemplate(options, "location")} />
                        <Column sortable field="zone" header="Zone" body={zoneTemplate} showFilterMatchModes={false}
                            filter filterElement={(options) => RowFilterTemplate(options, "zone")} />
                        <Column sortable field="cat" header="Category" body={categoryTemplate} showFilterMatchModes={false}
                            filter filterElement={(options) => RowFilterTemplate(options, "cat")} />
                        <Column field="grade" filter showFilterMatchModes={false}
                            filterElement={(options) => RowFilterTemplate(options, "grade")}
                            header="MSI Rating" body={ratingTemplate} />
                        <Column field="calibratorName" header="Calibration Team Member" body={(rowData) => getCalibratorName(rowData)}
                            showFilterMatchModes={false} filter filterElement={(options) => RowFilterTemplate(options, "calibratorName")} />
                        <Column header="View Report" body={viewSubmissionTemplate} />
                    </DataTable>
                </TabPanel>
            </TabView>

            {/* Dialog for viewing submission details */}
            <Dialog
                visible={submissionDialog}
                onHide={() => {
                    setSubmissionDialog(false);
                    setSelectedAudit(null);
                }}
                style={{ width: '80vw' }}
                className="custom-dialog"
            >
                {submissionData ? (
                  <></>  // <DealerSubmissionView excelData={submissionData} dealerInfo={selectedAudit} />
                ) : (
                    <p>No submission data available.</p>
                )}
            </Dialog>

            {/* Dialog for viewing report details */}
            <Dialog
                visible={reportDialog}
                className="custom-dialog"
                style={{ width: 1200 }}
                onHide={() => { setReportDialog(false) }}
            >
                {/* <DealerCompletedReport report={selectedAudit} /> */}
            </Dialog>
        </div>
    );
};

export default MSISubmissionsTab;

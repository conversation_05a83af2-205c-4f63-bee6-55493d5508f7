import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { Checkbox } from "@mui/material";

// Sample data for emissions over time (years)
const dummyData = [
  {
    year: 2020,
    scope1: 120,
    scope2: 90,
    scope3: 150,
  },
  {
    year: 2021,
    scope1: 130,
    scope2: 85,
    scope3: 145,
  },
  {
    year: 2022,
    scope1: 140,
    scope2: 80,
    scope3: 160,
  },
  {
    year: 2023,
    scope1: 145,
    scope2: 75,
    scope3: 170,
  },
];

const EmissionTrendsChart = () => {
  const [visibleSeries, setVisibleSeries] = useState({
    scope1: true,
    scope2: true,
    scope3: true,
  });
  const chartRef = useRef(null);

  useEffect(() => {
    renderLineChart();
  }, [visibleSeries]);

  const renderLineChart = () => {
    const width = 600;
    const height = 400;
    const margin = { top: 20, right: 30, bottom: 40, left: 40 };

    // Clear any existing SVG
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Define scales
    const x = d3
      .scaleLinear()
      .domain([2020, 2023])
      .range([0, width - margin.left - margin.right]);
    const y = d3
      .scaleLinear()
      .domain([0, 200])
      .range([height - margin.top - margin.bottom, 0]);

    // Line generator
    const line = d3
      .line()
      .x((d) => x(d.year))
      .y((d) => y(d.value));

    // Draw the lines for each scope
    const seriesData = [
      {
        name: "Scope 1",
        color: "#e74c3c",
        data: dummyData.map((d) => ({ year: d.year, value: d.scope1 })),
      },
      {
        name: "Scope 2",
        color: "#f39c12",
        data: dummyData.map((d) => ({ year: d.year, value: d.scope2 })),
      },
      {
        name: "Scope 3",
        color: "#16a085",
        data: dummyData.map((d) => ({ year: d.year, value: d.scope3 })),
      },
    ];

    seriesData.forEach((series) => {
      if (visibleSeries[series.name.toLowerCase().replace(" ", "")]) {
        svg
          .append("path")
          .data([series.data])
          .attr("class", "line")
          .attr("d", line)
          .attr("fill", "none")
          .attr("stroke", series.color)
          .attr("stroke-width", 2);
      }
    });

    // Add axes
    svg
      .append("g")
      .attr("transform", `translate(0,${height - margin.top - margin.bottom})`)
      .call(d3.axisBottom(x).tickFormat(d3.format("d"))); // Format ticks as years

    svg.append("g").call(d3.axisLeft(y));

    // Add labels
    svg
      .append("text")
      .attr("x", width / 2)
      .attr("y", height - margin.bottom + 20)
      .style("text-anchor", "middle")
      .text("Year");

    svg
      .append("text")
      .attr("x", -height / 2)
      .attr("y", -margin.left + 20)
      .style("text-anchor", "middle")
      .text("Emissions");

    // Add legends
    const legend = svg
      .append("g")
      .attr("transform", `translate(${width - 150},10)`);

    seriesData.forEach((series, index) => {
      const legendItem = legend
        .append("g")
        .attr("transform", `translate(0, ${index * 20})`);

      legendItem
        .append("rect")
        .attr("width", 18)
        .attr("height", 18)
        .attr("fill", series.color);

      legendItem
        .append("text")
        .attr("x", 24)
        .attr("y", 9)
        .attr("dy", ".35em")
        .style("font-size", "14px")
        .text(series.name);
    });
  };

  const handleCheckboxChange = (key) => {
    setVisibleSeries((prevState) => ({
      ...prevState,
      [key]: !prevState[key],
    }));
  };

  return (
    <div>
      <div
        style={{
          fontFamily: "Lato",
          fontSize: "16px",
          fontWeight: 700,
          lineHeight: "19.2px",
          textAlign: "left",
          marginBottom: "10px",
        }}
      >
        Emission Trends Over Time
        <div style={{ fontWeight: 200, fontSize: "14px" }}>
          Track emissions from Scope 1, 2, and 3 across different years.
        </div>
      </div>
      <div ref={chartRef} style={{ textAlign: "center" }} />

      {/* Checkbox for Scope 1, Scope 2, and Scope 3 visibility */}
      <div style={{ textAlign: "center", marginTop: "20px" }}>
        {["scope1", "scope2", "scope3"].map((scope) => (
          <div
            key={scope}
            style={{ display: "inline-block", marginRight: "20px" }}
          >
            <Checkbox
              checked={visibleSeries[scope]}
              onChange={() => handleCheckboxChange(scope)}
              style={{
                color:
                  scope === "scope1"
                    ? "#e74c3c"
                    : scope === "scope2"
                    ? "#f39c12"
                    : "#16a085",
                marginRight: 4,
                fontSize: "20px",
              }}
            />
            <span style={{ color: "#555", fontSize: "14px" }}>
              {scope === "scope1"
                ? "Scope 1"
                : scope === "scope2"
                ? "Scope 2"
                : "Scope 3"}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EmissionTrendsChart;

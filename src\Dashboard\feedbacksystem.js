import React, { useState, useEffect, useRef } from "react";
import { InputText } from "primereact/inputtext";
import { InputTextarea } from "primereact/inputtextarea";
import { But<PERSON> } from "primereact/button";
import { Editor } from "primereact/editor";
import APIServices from "../service/APIService";
import Swal from "sweetalert2";
import { API } from "../constants/api_url";
import { useSelector } from "react-redux";
import { Dropdown } from "primereact/dropdown";
import { Toast } from "primereact/toast";

const FeedbackSystem = () => {
    const [subject, setSubject] = useState("");
    const [message, setMessage] = useState("");
    const [type, setType] = useState(null);
    const [priority, setPriority] = useState(null);
    const [category, setCategory] = useState(null);
    const [selectedFile, setSelectedFile] = useState(null);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [fileSizeError, setFileSizeError] = useState("");

    const toast = useRef(null);
    const fileInputRef = useRef(null);

    const admin_data = useSelector((state) => state.user.admindetail);
    const login_data = useSelector((state) => state.user.userdetail);
    const vendorCode = useSelector((state) => state.user.currentVendorCode);
    const priority_list = [{ name: 'Low', id: 1 }, { name: 'Medium', id: 2 }, { name: 'High', id: 3 }]
    const category_list = [{ name: 'Techincal', id: 1 }, { name: 'Sustainability subject related issue', id: 2 }]
    const type_list = [
        { name: "Login & Access Issue", id: 1, category: 1 },
        { name: "Account locked", id: 2, category: 1 },
        { name: "Submission & Data Entry Issues", id: 3, category: 1 },
        { name: "Unable to submit data", id: 4, category: 1 },
        { name: "System crashes during data upload", id: 5, category: 1 },
        { name: "File upload errors (e.g., invalid format, file size restrictions)", id: 6, category: 1 },
        { name: "Form or Assessment not loading / crashes", id: 7, category: 1 },
        { name: "Unable to save responses", id: 8, category: 1 },
        { name: "UI/UX & Navigation Issues", id: 9, category: 1 },
        { name: "Drop-downs, buttons, or input fields not functioning", id: 10, category: 1 },
        { name: "Other Technical Issues", id: 11, category: 1 },
        { name: "Any other bug or system-related issue", id: 12, category: 1 },
        { name: "Difficulty in mapping sustainability data to specific frameworks (GRI, BRSR, CDP, etc.)", id: 13, category: 2 },
        { name: "Incorrect calculations for emissions, water usage, or energy consumption", id: 14, category: 2 },
        { name: "Misalignment of submitted data with reporting requirements", id: 15, category: 2 },
        { name: "Uncertainty in interpreting framework-specific guidelines (e.g., GRI vs. CSRD vs. BRSR)", id: 16, category: 2 },
        { name: "Difficulty linking evidence to specific ESG indicators", id: 17, category: 2 },
        { name: "Incorrect role assignment leading to data access restrictions", id: 18, category: 2 },
        { name: "Lack of training on sustainability reporting and frameworks", id: 19, category: 2 },
        { name: "Requests for additional sustainability metrics to align with emerging frameworks", id: 20, category: 2 }

    ]

    // Function to validate file sizes
    const validateFileSize = (files) => {
        if (!files || files.length === 0) {
            setFileSizeError("");
            return true;
        }

        const maxSizeInBytes = 10 * 1024 * 1024; // 10MB in bytes
        let totalSize = 0;
        const fileDetails = [];

        // Calculate total size and collect file details
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            totalSize += file.size;
            fileDetails.push({
                name: file.name,
                size: (file.size / (1024 * 1024)).toFixed(2) // Size in MB
            });
        }

        if (totalSize > maxSizeInBytes) {
            const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2);
            const fileList = fileDetails.map(f => `${f.name} (${f.size}MB)`).join(', ');

            setFileSizeError(
                `Total file size (${totalSizeMB}MB) exceeds the 10MB limit. Selected files: ${fileList}`
            );
            return false;
        }

        setFileSizeError("");
        return true;
    };

    // Function to handle file selection
    const handleFileChange = (e) => {
        const files = e.target.files;

        if (validateFileSize(files)) {
            console.log(files);
            setSelectedFile(files);
        } else {
            // Clear the file input if validation fails
            e.target.value = "";
            setSelectedFile(null);
        }
    };

    const handleSubmitWithAttachment = (e) => {
        e.preventDefault();

        // Validate file sizes before submission
        if (selectedFile && !validateFileSize(selectedFile)) {
            Swal.fire({
                title: "File Size Error",
                text: "Please select files with a total size of 10MB or less.",
                icon: "error",
                confirmButtonText: 'OK',
            });
            return;
        }

        if (!checkEditorValue(message) && subject.trim().length && type && priority) {
            setIsSubmitting(true);

            // Create FormData object
            const formData = new FormData();

            // Add files if selected (handle multiple files)
            if (selectedFile && selectedFile.length > 0) {
                for (let i = 0; i < selectedFile.length; i++) {
                    formData.append('file', selectedFile[i]);
                }
            }


            // Add other form fields
            formData.append('company_name', admin_data?.information?.companyname || '');
            formData.append('company_name_id', admin_data?.id || '');
            formData.append('email', login_data.email);
            formData.append('requester_role', login_data.role === 'clientadmin' ? 'Admin' :
                login_data.role === 'clientuser' ? 'User' :
                    login_data.role === 'clientdealer' ? 'Dealer' : login_data.role === 'clientsupplier' ? 'Supplier' : login_data.role === 'clientextuser' ? 'External User' : 'Other');
            formData.append('category', category_list.find(x => x.id === category)?.name || '');
            formData.append('type', type_list.find(x => x.id === type)?.name || '');
            formData.append('priority', priority_list.find(x => x.id === priority)?.name || '');
            formData.append('subject', subject);
            if (vendorCode && (login_data.role === 'clientsupplier' || login_data.role === 'clientdealer')) {
                formData.append('vendor_name', vendorCode.supplierName || vendorCode.dealerName);
                formData.append('vendor_code', vendorCode.code);

                if (login_data.role === 'clientsupplier') {
                    formData.append('user_name', vendorCode?.supplierSPOC || '');
                } else if (login_data.role === 'clientdealer') {
                    formData.append('user_name', vendorCode?.dealerSPOC || '');
                }

            } else if ((login_data.role !== 'clientsupplier' && login_data.role !== 'clientdealer')) {
                formData.append('user_name', login_data?.information?.empname || '');
            }

            // Create formatted HTML description
            const formattedDescription = message;

            formData.append('description', formattedDescription);


            // Send to Zoho Ticket Upload API
            APIServices.post('https://cx3fkulxf5ae6colo73hzirxai0ulngx.lambda-url.ap-south-1.on.aws/', formData, {
                headers: {
                    'content-type': 'multipart/form-data'
                }
            }).then(() => {
                // Also create a ticket in the internal system
                APIServices.post(API.Ticketing_UP(admin_data.id), {
                    raised_by: login_data.id,
                    message: formattedDescription,
                    subject,
                    priority,
                    category: type,
                    userType: login_data.role === 'clientadmin' ? 1 : login_data.role === 'clientuser' ? 2 : login_data.role === 'clientdealer' ? 3 : login_data.role === 'clientsupplier' ? 4 : login_data.role === 'clientextuser' ? 5 : 0
                }).catch((e) => { console.log(e) });

                // Reset form
                setMessage("");
                setSubject("");
                setPriority(null);
                setType(null);
                setCategory(null);
                setSelectedFile(null);
                setFileSizeError("");

                // Reset file input
                if (fileInputRef.current) {
                    fileInputRef.current.value = "";
                }

                setIsSubmitting(false);

                // Show success message
                Swal.fire({
                    title: "Ticket raised successfully, will get back to you soon",
                    confirmButtonText: 'Close Popup',
                    allowOutsideClick: false,
                });
            }).catch((err) => {
                console.log(err);
                setIsSubmitting(false);

                // Show error message
                Swal.fire({
                    title: "Something went wrong",
                    text: "Please try again later",
                    icon: "error",
                    confirmButtonText: 'Close Popup',
                    allowOutsideClick: false,
                });
            });
        }
    };
    function checkEditorValue(htmlString) {
        if (!htmlString) {
            return true
        }
        const regex = /^<p>\s*<\/p>$/;
        return regex.test(htmlString);
    }
    return (
        <div className="flex justify-center" style={{ flexDirection: "column" }}>
            {[289,51,136,291].includes(admin_data.id)  ?
                <>
                    <div
                        className="flex col-12 flex-start"
                        style={{ flexDirection: "column" }}
                    >
                        <span className="text-big-one">
                            NAVIGOS Ticketing System
                        </span>
                        <p className="ml-1">We value your feedback and are here to help with any issues you’re facing! Share the issues you’ve encountered, and feel free to include any details that might help us understand better. Your input is important, and we’ll ensure to follow up with you promptly. Thank you for helping us improve ! </p>
                        {/* <Tag className="ml-3 p-tag-blue">

              {login_data.role === "clientadmin"
                ? "Enterprise Admin"
                : getRoles(login_data.information)}
            </Tag> */}
                    </div>
                    <div className="p-2">
                        <form
                            onSubmit={handleSubmitWithAttachment}
                            className="p-fluid rounded-lg"
                            style={{ width: "100%" }}
                        >
                            <div className="field col-4 p-0">
                                <label htmlFor="subject" className="block text-lg font-medium mb-2">
                                    Choose Issue Category<span className="ml-1 mandatory">*</span>
                                </label>
                                <Dropdown
                                    id="type"
                                    options={category_list}
                                    onChange={(e) => { setCategory(e.value); setType(null) }}
                                    value={category}
                                    optionValue="id"
                                    optionLabel="name"
                                    placeholder="Select Issue Category"
                                />
                            </div>
                            <div className="field col-4 p-0">
                                <label htmlFor="subject" className="block text-lg font-medium mb-2">
                                    Choose Issue Type <span className="ml-1 mandatory">*</span>
                                </label>
                                <Dropdown
                                    id="category"
                                    options={type_list.filter(x => x.category === category)}
                                    onChange={(e) => { setType(e.value) }}
                                    value={type}
                                    optionValue="id"
                                    optionLabel="name"
                                    placeholder="Select Issue Type"
                                />
                            </div>
                            <div className="field col-4 p-0">
                                <label htmlFor="subject" className="block text-lg font-medium mb-2">
                                    Choose Priority <span className="ml-1 mandatory">*</span>
                                </label>
                                <Dropdown
                                    id="priority"
                                    options={priority_list}
                                    onChange={(e) => { setPriority(e.value) }}
                                    placeholder="Select priority"
                                    value={priority}
                                    optionValue="id"
                                    optionLabel="name"
                                />
                            </div>
                            <div className="field">
                                <label htmlFor="subject" className="block text-lg font-medium mb-2">
                                    Subject <span className="ml-1 mandatory">*</span>
                                </label>
                                <InputText
                                    id="subject"
                                    value={subject}
                                    onChange={(e) => setSubject(e.target.value)}
                                    placeholder="Enter subject"
                                />
                            </div>

                            <div className="field mt-4">
                                <label htmlFor="message" className="block fs-14 text-lg font-medium mb-2">
                                    Message <span className="ml-1 mandatory">*</span>
                                </label>
                                <Editor
                                    id="message"
                                    value={message}
                                    style={{ resize: "none", height: 300, overflowY: 'scroll' }}
                                    onTextChange={(e) => setMessage(e.htmlValue)}
                                    rows={5}
                                    placeholder="Enter your message"
                                    className="text-editor-2"

                                    formats={['bold', 'italic', 'underline', 'list', 'bullet', 'link']}
                                    onLoad={(quill) => {
                                        // Prevent image pasting
                                        quill.root.addEventListener('paste', (e) => {
                                            const clipboardData = e.clipboardData;
                                            if (clipboardData) {
                                                const items = clipboardData.items;
                                                for (let i = 0; i < items.length; i++) {
                                                    if (items[i].type.indexOf('image') !== -1) {
                                                        e.preventDefault();
                                                        alert('Images are not allowed. Please use the attachment field to upload files.');
                                                        return false;
                                                    }
                                                }
                                            }
                                        });
                                    }}
                                />
                            </div>

                            <div className="field mt-4">
                                <label htmlFor="attachment" className="block fs-14 text-lg font-medium mb-2">
                                    Attachment
                                </label>
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    id="attachment"
                                    multiple
                                    className={`p-2 border-1 surface-border border-round w-full ${fileSizeError ? 'border-red-500' : ''}`}
                                    onChange={handleFileChange}
                                    accept=".doc,.docx,.pdf,.jpg,.jpeg,.png,.gif,.xls,.xlsx,.csv"
                                />
                                {fileSizeError && (
                                    <small className="text-red-500 mt-1 block">
                                        {fileSizeError}
                                    </small>
                                )}
                                <small className="text-gray-500 mt-1 block">
                                    Supported formats: Images, PDF, Word documents, Excel files (Max total size: 10MB)
                                </small>
                            </div>

                            <div className="col-12 flex justify-content-end mt-4">
                                <Toast ref={toast} />
                                <Button
                                    disabled={!subject.length || checkEditorValue(message) || !type || !category || !priority || isSubmitting || fileSizeError}
                                    type="submit"
                                    label={isSubmitting ? "Submitting..." : "Raise Ticket"}
                                    icon="pi pi-send"
                                    style={{ width: 'auto', padding: 10 }}
                                    loading={isSubmitting}
                                />
                            </div>
                        </form>
                    </div>  </> :
                'You have no access to view this page'
            }

        </div>
    );
};

export default FeedbackSystem;

.status-tag-platinum {
    background-color: #8A2BE2 !important;
    color: white !important;
}

.status-tag-gold {
    background-color: #FFD700 !important;
    color: black !important;
}

.status-tag-silver {
    background-color: #C0C0C0 !important;
    color: black !important;
}

.status-tag-bronze {
    background-color: #CD7F32 !important;
    color: white !important;
}

.status-tag-needs-improvement {
    background-color: #FF6347 !important;
    color: white !important;
}

.status-tag-gray {
    background-color: #808080 !important;
    color: white !important;
}

.status-tag-green {
    background-color: #4CAF50 !important;
    color: white !important;
}

.status-tag-orange {
    background-color: #FF9800 !important;
    color: white !important;
}

.custom-dialog .p-dialog-content {
    padding: 0;
    overflow: hidden;
}

.h-500 {
    max-height: 500px;
    overflow: auto;
}

/* Styling for the filter dropdowns */
.p-column-filter {
    width: 100%;
}

.hidefilter .p-multiselect-filter-container {
    display: none;
}

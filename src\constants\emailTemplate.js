const DCFSubmissionReturn = (name, form, site, rp, mail_remarks) => {
    return `<p>${name} has returned the following data for correction on the  ESG Portal.</p><p><strong>${form}</strong></p><p><strong>Site:</strong> ${site !== undefined && site !== null && site}</p><p><strong>Reporting Period:</strong> ${rp}</p><p><strong>Remarks :</strong> <p style="color:red;">${mail_remarks}  </p></p><p>Please login to the portal to review the submission</p><p><a href=https://navigos.eisqr.com>click here</a> to access  ESG Reporter Platform</p><hr/><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`
}
const DCFSubmission = (name, form, site, rp, mail_remarks, type) => {
    console.log(name, form, site, rp, mail_remarks, type)
    return `<p>${name} has ${type === 0 ? 'submitted ' : 'resubmitted '} the following data for approval on the  ESG Portal.</p><p><strong>${form}</strong></p><p><strong>Site:</strong> ${site !== undefined && site !== null && site}</p><p><strong>Reporting Period:</strong> ${rp}</p><p><strong>Remarks:</strong> ${mail_remarks}</p><p>Please login to the portal to review the submission</p><p><a href=https://navigos.eisqr.com>click here</a> to access  ESG Approver Platform</p><hr/><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`
}

export {DCFSubmission,DCFSubmissionReturn}
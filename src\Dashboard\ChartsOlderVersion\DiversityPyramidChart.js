import React, { useEffect, useRef } from "react";
import * as d3 from "d3";

const DiversityPyramidChart = () => {
  const chartRef = useRef(null);

  const diversityData = [
    { level: "Entry-level", male: 120, female: 100, other: 10 },
    { level: "Mid-level", male: 80, female: 90, other: 5 },
    { level: "Senior Management", male: 50, female: 40, other: 2 },
    { level: "Executive", male: 20, female: 10, other: 1 },
  ];

  const filter = "male"; // Default filter

  useEffect(() => {
    renderPyramidChart();
  }, []);

  const renderPyramidChart = () => {
    const width = 600;
    const height = 400;
    const margin = { top: 20, right: 40, bottom: 20, left: 40 };

    // Clear existing SVG
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height);

    const filteredData = diversityData.map((d) => ({
      level: d.level,
      count: d[filter],
    }));

    const levels = filteredData.map((d) => d.level);
    const counts = filteredData.map((d) => d.count);

    const xScale = d3
      .scaleLinear()
      .domain([0, d3.max(counts)])
      .range([0, width / 2]);

    const yScale = d3
      .scaleBand()
      .domain(levels)
      .range([height - margin.bottom, margin.top])
      .padding(0.5);

    // Draw pyramid bars
    svg
      .selectAll(".bar")
      .data(filteredData)
      .enter()
      .append("rect")
      .attr("x", (d) => width / 2 - xScale(d.count))
      .attr("y", (d) => yScale(d.level))
      .attr("width", (d) => xScale(d.count) * 2)
      .attr("height", yScale.bandwidth())
      .attr("fill", "#3498db");

    // Add text labels (white, centered inside the bars)
    svg
      .selectAll(".text-label")
      .data(filteredData)
      .enter()
      .append("text")
      .attr("x", width / 2) // Center horizontally
      .attr("y", (d) => yScale(d.level) + yScale.bandwidth() / 2)
      .attr("text-anchor", "middle")
      .attr("dy", ".35em")
      .text((d) => `${d.level} (${d.count})`)
      .style("fill", "#fff")
      .style("font-size", "12px");
  };

  return (
    <>
      <div
        style={{
          fontSize: "14px",
          fontWeight: 700,
          lineHeight: "19.2px",
          textAlign: "left",
          marginBottom: "5px",
        }}
      >
        Diversity Pyramid Chart
      </div>
      <div>
        Represent employee diversity by levels (e.g., entry-level, mid-level,
        senior management), with filters for gender, age, and other metrics.
      </div>
      <div
        style={{
          display: "flex",
          justifyContent: "center", // Center horizontally
          alignItems: "center",
          flexDirection: "column",
          height: "100%",
        }}
      >
        <div ref={chartRef} style={{ marginTop: "20px" }}></div>
      </div>
    </>
  );
};

export default DiversityPyramidChart;

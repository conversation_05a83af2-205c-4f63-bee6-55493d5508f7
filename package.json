{"name": "eisqr", "version": "1.0.0", "homepage": "/", "private": false, "license": "MIT", "dependencies": {"@emotion/react": "^11.11.4", "@fullcalendar/core": "^5.7.2", "@material-ui/core": "^4.12.4", "@mui/icons-material": "^6.1.10", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.21", "@mui/styles": "^5.15.21", "@react-google-maps/api": "^2.20.3", "@reduxjs/toolkit": "^1.8.6", "@weknow/react-bubble-chart-d3": "^1.0.12", "axios": "^0.19.0", "bootstrap": "^5.3.2", "chart.js": "^3.7.1", "chartjs-chart-sankey": "^0.13.0", "chartjs-plugin-trendline": "^2.0.2", "classnames": "^2.2.6", "crypto-js": "^4.2.0", "d3": "^7.9.0", "d3-sankey": "^0.12.3", "d3-scale-chromatic": "^3.1.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "formik": "^2.2.9", "framer-motion": "^4.1.17", "jquery": "^3.6.1", "leaflet": "^1.9.4", "leaflet.heat": "^0.2.0", "luxon": "^3.3.0", "material-table": "^2.0.5", "moment": "^2.29.4", "primeflex": "3.1.0", "primeicons": "^5.0.0", "primereact": "^9.6.4", "prismjs": "1.9.0", "quill": "^1.3.7", "react": "^17.0.1", "react-app-polyfill": "^1.0.6", "react-bootstrap": "^2.9.1", "react-dom": "^17.0.1", "react-edit-text": "^5.0.2", "react-gauge-chart": "^0.5.1", "react-leaflet": "^4.2.1", "react-redux": "^8.0.4", "react-router-dom": "^5.2.0", "react-scripts": "^4.0.3", "react-sticky": "^6.0.3", "react-to-print": "^3.0.5", "react-transition-group": "^4.4.1", "recharts": "^2.13.2", "redux": "^4.2.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "sass": "^1.81.0", "sweetalert2": "^11.4.33", "use-force-update": "^1.0.10", "uuid": "^10.0.0", "xlsx": "^0.18.5", "xlsx-populate": "^1.21.0", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts --openssl-legacy-provider start", "build": "react-scripts --openssl-legacy-provider build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "overrides": {"@react-pdf/font": "2.2.1", "@react-pdf/pdfkit": "2.1.0"}}